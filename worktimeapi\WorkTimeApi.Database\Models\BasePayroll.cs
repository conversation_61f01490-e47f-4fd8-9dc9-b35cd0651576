﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Database.Models
{
    public abstract class BasePayroll
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public string? ContractNumber { get; set; }

        public InsuranceTerminationCode? CodeOfInsuranceTermination { get; set; }

        public TerminationReason? TerminationReason { get; set; }

        public PermanentContractType? PermanentContractType { get; set; }

        public int? PayrollWorkTime { get; set; }

        public decimal? PayrollSalary { get; set; }

        public DateTime? ContractDate { get; set; }

        public DateTime? ContrractEndDate { get; set; }

        public DateTime? ContrractTermDate { get; set; }

        public string? Note { get; set; }

        public bool IsDeleted { get; set; }

        public string? Location { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public bool IsFromEducationSystem { get; set; }

        public int NotificationCorrectionCode { get; set; }

        public ContractReason? ContractReason { get; set; }

        public TypeOfAppointment? ContractType { get; set; }

        public string? Occupation { get; set; }

        public string? Position { get; set; }

        public int? MODCode { get; set; }

        public IncomeType? IncomeType { get; set; }

        public string? EKATTECode { get; set; }

        public DateTime UploadDate { get; set; }

        public PayrollCategories PayrollCategory { get; set; }

        public Guid StructureLevelId { get; set; }

        public required StructureLevel StructureLevel { get; set; }
    }
}