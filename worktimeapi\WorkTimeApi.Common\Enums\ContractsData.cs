﻿using WorkTimeApi.Common.EnumUtilities;

namespace WorkTimeApi.Common.Enums
{
    public enum PermanentContractType
    {
        [MultiLangDescription("Основен", "Main")]
        PermanentContractType_Main = 1,
        [MultiLangDescription("Допълнителен/Втори ТД", "Additional/Second TD")]
        PermanentContractType_Additional = 2
    }

    public enum ContractReason : int
    {
        [MultiLangDescription("Безсрочен трудов договор по чл.67 ал.1 т.1 от КТ", "Permanent employment contract under Art.67, para.1, item 1 of the Labour Code")]
        ContractReason_Безсрочен_трудов_договор_чл67_ал1_т1 = 1,

        [MultiLangDescription("Срочен трудов договор по чл.68, ал.1, т.1 от КТ или чл.68, ал.6 от КТ", "Fixed-term employment contract under Art.68, para.1, item 1 or Art.68, para.6 of the Labour Code")]
        ContractReason_Срочен_трудов_договор_чл68ал1т1_или_чл68ал6 = 2,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.2 от КТ", "Employment contract under Art.68, para.1, item 2 of the Labour Code")]
        ContractReason_Трудов_договор_чл68_ал1_т2 = 3,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.3 от КТ", "Employment contract under Art.68, para.1, item 3 of the Labour Code")]
        ContractReason_Трудов_договор_чл68_ал1_т3 = 4,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.4 от КТ", "Employment contract under Art.68, para.1, item 4 of the Labour Code")]
        ContractReason_Трудов_договор_чл68_ал1_т4 = 5,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.5 от КТ", "Employment contract under Art.68, para.1, item 5 of the Labour Code")]
        ContractReason_Трудов_договор_чл68_ал1_т5 = 6,

        [MultiLangDescription("Трудов договор със срок за изпитване по чл.70 от КТ", "Employment contract with probationary period under Art.70 of the Labour Code")]
        ContractReason_Трудов_договор_със_срок_за_изпитванe_чл70 = 7,

        [MultiLangDescription("Споразумение по чл.107 КТ във връзка с чл.83 от КТ", "Agreement under Art.107 in relation to Art.83 of the Labour Code")]
        ContractReason_Споразумение_чл107_във_връзка_чл83 = 8,

        [MultiLangDescription("Споразумение по чл.107 КТ във връзка с чл.89 от КТ", "Agreement under Art.107 in relation to Art.89 of the Labour Code")]
        ContractReason_Споразумение_чл107_във_връзка_чл89 = 9,

        [MultiLangDescription("Допълнителен трудов договор по чл.110 от КТ", "Additional employment contract under Art.110 of the Labour Code")]
        ContractReason_Допълнителен_трудов_договор_чл110 = 10,

        [MultiLangDescription("Допълнителен трудов договор по чл.111 от КТ", "Additional employment contract under Art.111 of the Labour Code")]
        ContractReason_Допълнителен_трудов_договор_чл111 = 11,

        [MultiLangDescription("Трудов договор по чл.114, ал.1 от КТ", "Employment contract under Art.114, para.1 of the Labour Code")]
        ContractReason_Трудов_договор_чл114 = 12,

        [MultiLangDescription("Постановление по чл.405а от КТ", "Decree under Art.405a of the Labour Code")]
        ContractReason_Постановление_по_чл405а = 13,

        [MultiLangDescription("Трудов договор за обучение по време на работа по чл.230 КТ", "Apprenticeship employment contract under Art.230 of the Labour Code")]
        ContractReason_Трудов_договор_за_ученичество_чл230 = 14,

        [MultiLangDescription("Трудов договор за вътрешно заместване по чл.259 от КТ", "Employment contract for internal substitution under Art.259 of the Labour Code")]
        ContractReason_Трудов_договор_за_вътрешно_заместване_чл259 = 15,

        [MultiLangDescription("Трудов договор за стажуване по чл.233б КТ", "Internship employment contract under Art.233b of the Labour Code")]
        ContractReason_Трудов_договор_чл233б = 16,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.2 или т.3 във връзка с чл.121а, ал.2, т.1 от КТ", "Employment contract under Art.68, para.1, item 2 or 3 in relation to Art.121a, para.2, item 1 of the Labour Code")]
        ContractReason_Трудов_договор_чл68ал1т2_или_т3_във_връзка_чл121а_ал2т1 = 17,

        [MultiLangDescription("Допълнително споразумение в случаите на чл.121а, ал.1, т.1 от КТ", "Additional agreement under Art.121a, para.1, item 1 of the Labour Code")]
        ContractReason_Допълнително_споразумение_в_случаите_на_чл121а_ал1т1 = 18,

        [MultiLangDescription("Трудов договор по чл.114а от КТ", "Employment contract under Art.114a of the Labour Code")]
        ContractReason_Трудов_договор_чл114а = 19,

        [MultiLangDescription("Трудов договор по чл.68 ал.1 т.5 от КТ", "Employment contract under Art. 68, para. 1, item 5 of the Labour Code")]
        ContractReason_Трудов_договор_чл68ал1т5 = 20,

        [MultiLangDescription("Трудов договор по чл.68, ал.1, т.2 или т.3 във връзка с чл.121а, ал.2, т.1 от КТ", "Employment contract under Art. 68, para. 1, items 2 or 3 in connection with Art. 121a, para. 2, item 1 of the Labour Code")]
        ContractReason_Трудов_договор_чл68ал1т2и3 = 21,

        [MultiLangDescription("Трудов договор със срок за изпитване по чл.70 от КТ", "Employment contract with a probationary period under Art. 70 of the Labour Code")]
        ContractReason_Трудов_договор_чл70 = 22,
    }

    public enum TerminationReason
    {
        TerminationReason_Не_е_избрано = 0,
        TerminationReason_чл71ал1КТ = 1,
        TerminationReason_чл325ал1т1 = 2,
        TerminationReason_чл325ал1т2 = 3,
        TerminationReason_чл325ал1т3 = 4,
        TerminationReason_чл325ал1т4 = 5,
        TerminationReason_чл325ал1т5 = 6,
        TerminationReason_чл325ал1т6 = 7,
        TerminationReason_чл325ал1т8 = 8,
        TerminationReason_чл325ал1т9 = 9,
        TerminationReason_чл325ал1т10 = 10,
        TerminationReason_чл325ал1т11 = 11,
        TerminationReason_чл325ал1т12 = 12,
        TerminationReason_чл325ал2 = 13,
        TerminationReason_чл326ал1 = 14,
        TerminationReason_чл327ал1т1 = 15,
        TerminationReason_чл327ал1т2 = 16,
        TerminationReason_чл327ал1т3 = 17,
        TerminationReason_чл327ал1т3а = 18,
        TerminationReason_чл327ал1т4 = 19,
        TerminationReason_чл327ал1т6 = 20,
        TerminationReason_чл327ал1т7 = 21,
        TerminationReason_чл327ал1т7а = 22,
        TerminationReason_чл327ал1т8 = 23,
        TerminationReason_чл327ал1т9 = 24,
        TerminationReason_чл327ал1т10 = 25,
        TerminationReason_чл327ал1т11 = 26,
        TerminationReason_чл327ал1т12 = 27,
        TerminationReason_чл328ал1т1 = 28,
        TerminationReason_чл328ал1т2 = 29,
        TerminationReason_чл328ал1т3 = 30,
        TerminationReason_чл328ал1т4 = 31,
        TerminationReason_чл328ал1т5 = 32,
        TerminationReason_чл328ал1т6 = 33,
        TerminationReason_чл328ал1т7 = 34,
        TerminationReason_чл328ал1т8 = 35,
        TerminationReason_чл328ал1т10 = 36,
        TerminationReason_чл328ал1т10а = 37,
        TerminationReason_чл328ал1т10б = 38,
        TerminationReason_чл328ал1т10в = 39,
        TerminationReason_чл328ал1т11 = 40,
        TerminationReason_чл328ал1т12 = 41,
        TerminationReason_чл328ал2 = 42,
        TerminationReason_чл330ал1 = 43,
        TerminationReason_чл330ал2т1 = 44,
        TerminationReason_чл330ал2т2 = 45,
        TerminationReason_чл330ал2т3 = 46,
        TerminationReason_чл330ал2т5 = 47,
        TerminationReason_чл330ал2т6 = 48,
        TerminationReason_чл330ал2т7 = 49,
        TerminationReason_чл330ал2т8 = 50,
        TerminationReason_чл330ал2т9 = 51,
        TerminationReason_чл330ал2т10 = 52,
        TerminationReason_чл330ал2т11 = 53,
        TerminationReason_чл331 = 54,
        TerminationReason_чл334ал1 = 55,
        TerminationReason_чл337 = 56,
        TerminationReason_чл338 = 57,
        TerminationReason_чл19аал2ЗА = 58,
        TerminationReason_чл19аал3ЗА = 59,
        TerminationReason_чл340аал5т1ЗСВ = 60,
        TerminationReason_чл340аал5т2ЗСВ = 61,
        TerminationReason_чл340аал5т3ЗСВ = 62,
        TerminationReason_чл340аал6ЗСВ = 63,
        TerminationReason_друго = 64
    }

    public enum NotificationDocumentType
    {
        ContractEffective_As_Of_01_01_2003 = 0,
        ContractConcludedAfter_01_01_2003 = 1,
        AnnexPayrollAfter_01_01_2003 = 2,
        Termination_after_01_01_2003_of_a_registered_contract = 3
    }

    public enum InsuranceTerminationCode
    {
        InsuranceTerminationCode_Не_е_избрано = 0,
        InsuranceTerminationCode_Извън_посочените_по_чл54б_ал_3_от_КСО = 1,
        InsuranceTerminationCode_Посочени_в_чл54б_ал_3_от_КСО = 2
    }

    public enum PayrollCategories
    {
        [MultiLangDescription("Служители на фирма", "Company employees")]
        Employees = 0,
        [MultiLangDescription("Кандидатстващи за назначение", "Applicants for appointment")]
        ApplicantsForAppointment = 1,
        [MultiLangDescription("Външни услуги", "External services")]
        ExternalServices = 2
    }

    public enum TypeOfAppointment
    {
        [MultiLangDescription("Трудов договор", "Employment Contract")]
        EmploymentContracts = 0,
        [MultiLangDescription("Договор за управление", "Management Contract")]
        ManagementContracts = 1,
        [MultiLangDescription("Самоосигуряващо се лице", "Self-Employed Owner")]
        SelfEmployedOwner = 2,
        [MultiLangDescription("Граждански договор", "Civil Contract Standard")]
        CivilContractStandart = 3,
        [MultiLangDescription("Наемодател", "Landlord")]
        CivilContractLandlords = 4,
        [MultiLangDescription("Собственик с дивидент", "Owner with Dividend")]
        CivilContractDividends = 5,
        [MultiLangDescription("Други - ГД", "Other - Civil Contract")]
        CivilContractOther = 6,
        [MultiLangDescription("Служебно правоотношение", "Employment Relationship")]
        EmploymentRelationship = 7,
        [MultiLangDescription("Кандидати", "Candidates")]
        Candidates = 8,
        [MultiLangDescription("Одобрен за интервю", "Approved for Interview")]
        ApprovedForInterview = 9,
        [MultiLangDescription("Преминал интервю/среща", "Passed Interview/Meeting")]
        PassedInterviewMeeting = 10,
        [MultiLangDescription("Поканен на работа", "Invited to Work")]
        InvitedToWork = 11,
        [MultiLangDescription("За сключване на договор", "To Conclude Contract")]
        ToConcludeContract = 12,
        [MultiLangDescription("Представител на външна фирма", "Representative of an External Company")]
        RepresentativeOfAnExternalCompany = 13,
    }
}
