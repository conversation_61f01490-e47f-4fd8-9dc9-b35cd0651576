import { render, waitFor, cleanup } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import Attendance from "../Attendance";

jest.mock("../AttendancesRightView", () => () => <div data-testid="right" />);
jest.mock("../DatesTableContainer", () => () => <div data-testid="left" />);

// Create stable mock data to prevent infinite re-renders
const mockPayrolls = [
  {
    id: "payroll-1",
    workTimeId: "worktime-1",
    employee: {
      userId: "user-1",
      workTimeId: "emp-1",
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      egn: "1234567890",
    },
    structureLevelId: "lvl-1",
    leaves: [],
    annexPayrolls: [],
  },
];

jest.mock("../../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    userEmployee: {
      payrolls: mockPayrolls,
      permissions: ["Attendances.Write"],
    },
  }),
}));

// Mock company context
jest.mock("../../companies/CompanyContext", () => ({
  useCompany: () => ({
    company: { id: "company-1", name: "Company 1" },
    setCompany: jest.fn(),
    resetCompany: jest.fn(),
  }),
}));

// Mock Redux hooks to avoid Provider requirement
jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: () => ({ holidays: [] }),
}));

// Optional: avoid invoking real selector logic
jest.mock("../../holidays/holidayActions", () => ({
  onHolidaysLoaded: jest.fn(),
  selectHolidays: () => ({ holidays: [] }),
}));

describe("Attendance component", () => {
  afterEach(() => {
    cleanup();
  });

  test("renders without errors", () => {
    const initialUrl = "/company-1/attendance";
    const { getByTestId } = render(
      <MemoryRouter initialEntries={[initialUrl]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    expect(getByTestId("left")).toBeInTheDocument();
    expect(getByTestId("right")).toBeInTheDocument();
  });
});
