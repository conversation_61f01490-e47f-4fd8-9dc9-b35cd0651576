import React, { useEffect, useMemo, useRef, useState } from "react";
import DatesTableView, {
  DayInfo,
} from "../../components/CalendarComponent/DatesTableView";
import { AlignmentPosition } from "../../components/CalendarComponent/types/AlignmentPosition.ts";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { useFilteredEmployees, Employee } from "./useFilteredEmployees";
import { AbsenceInfo } from "../../components/CalendarComponent/types/AbsenceInfo.ts";
import { useCompany } from "../companies/CompanyContext.tsx";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus.ts";
import { calculateUniqueLeaveDays } from "../../services/calendar/calendarService.ts";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO.ts";
import { useModal } from "../../components/PopUp/ActionModalContext";
import { translate } from "../../services/language/Translator";
import { useAbsence } from "../absences/AbsenceContext";
import { useMenu } from "../MenuContext";
import { HolidayDTO } from "../../models/DTOs/Holidays/HolidaysDTO.ts";

interface DatesTableContainerProps {
  selectedPayroll?: LightPayrollDTO;
  setSelectedPayroll: (payroll: LightPayrollDTO | undefined) => void;
  selectedEmployee?: Employee;
  hoveredEmployee?: Employee;
  showMyAbsences: boolean;
  selectedMonth: number;
  selectedYear: number;
  setSelectedMonth: (month: number) => void;
  setSelectedYear: (year: number) => void;
  holidays: HolidayDTO[];
  highlightedAbsenceId?: string;
  isFromNotification: boolean;
}

const DatesTableContainer: React.FC<DatesTableContainerProps> = ({
  selectedPayroll,
  setSelectedPayroll,
  selectedEmployee,
  hoveredEmployee,
  showMyAbsences,
  selectedMonth,
  selectedYear,
  setSelectedMonth,
  setSelectedYear,
  holidays,
  highlightedAbsenceId,
  isFromNotification,
}) => {
  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls);
  const { setSelectedAbsence } = useAbsence();
  const { toggleMenu, changeView, isOpen } = useMenu();

  const { company } = useCompany();
  const [combinedDays, setCombinedDays] = useState<DayInfo[]>([]);
  const [maxRowsPerWeek, setMaxRowsPerWeek] = useState<{
    [week: number]: number;
  }>({});
  const { openModal } = useModal();
  const shownApprovedAbsenceIdRef = useRef<string | null>(null);

  const currentDate = new Date().getDate();
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();

  useEffect(() => {
    if (!payrolls.payrolls || payrolls.payrolls.length === 0) {
      dispatch(onPayrollsLoaded(company.id));
    }
  }, [dispatch, company.id]);

  useEffect(() => {
    setCombinedDays([]);
    setMaxRowsPerWeek({});
    shownApprovedAbsenceIdRef.current = null;
  }, [company.id]);

  const initialNotificationAbsenceIdRef = useRef<string | null>(null);
  useEffect(() => {
    if (
      isFromNotification &&
      initialNotificationAbsenceIdRef.current === null &&
      highlightedAbsenceId
    ) {
      initialNotificationAbsenceIdRef.current = highlightedAbsenceId;
    }
  }, [isFromNotification, highlightedAbsenceId]);

  useEffect(() => {
    if (!highlightedAbsenceId || !isFromNotification) return;
    if (
      initialNotificationAbsenceIdRef.current &&
      highlightedAbsenceId !== initialNotificationAbsenceIdRef.current
    )
      return;

    const leaves = payrolls.payrolls.flatMap((p) => p.leaves);
    const leave = leaves.find((l) => l.id === highlightedAbsenceId);
    if (
      leave &&
      leave.status === AbsenceStatus.Approved &&
      shownApprovedAbsenceIdRef.current !== highlightedAbsenceId
    ) {
      shownApprovedAbsenceIdRef.current = highlightedAbsenceId;
      openModal({
        type: "info",
        title: translate("strSelectedAbsenceAlreadyApproved"),
        confirmLabel: translate("Ok"),
      });
    }
  }, [
    highlightedAbsenceId,
    payrolls.payrolls,
    isFromNotification,
    initialNotificationAbsenceIdRef.current,
  ]);

  useEffect(() => {
    if (!highlightedAbsenceId) return;

    const payroll = payrolls.payrolls.find((p) =>
      p.leaves.some((l) => l.id === highlightedAbsenceId)
    );
    const leave = payroll?.leaves.find((l) => l.id === highlightedAbsenceId);
    if (!payroll || !leave) return;

    const absenceToSelect: AbsenceInfo = {
      id: leave.id ?? "",
      payrollId: leave.payrollId,
      userId: payroll.employee.userId,
      employeeName: `${payroll.employee.firstName || ""} ${
        payroll.employee.lastName || ""
      }`.trim(),
      row: 1,
      positonRounding: AlignmentPosition.Center,
      isHospital: leave.isHospital,
      status: leave.status,
      isHighlighted: true,
      startDate: leave.fromDate,
      endDate: leave.toDate,
      isOverlapping: leave.isOverlapping,
      typeIdentifier: leave.typeIdentifier,
      comment: leave.isHospital ? leave.sickNote ?? "" : leave.reference ?? "",
      sickNote: leave.sickNote ?? "",
      exportStatus: leave.exportStatus,
    };

    setSelectedAbsence(absenceToSelect);
    changeView("absence", "other", {
      selectedYear,
      selectedMonth,
    });

    if (!isOpen) toggleMenu();
  }, [highlightedAbsenceId, payrolls.payrolls, selectedMonth, selectedYear]);

  useEffect(() => {
    if (!highlightedAbsenceId) {
      setSelectedAbsence(null);
    }
  }, [highlightedAbsenceId]);

  const usedLeaveThisMonth = useMemo(() => {
    var leaves = selectedEmployee
      ? selectedEmployee.payrolls.flatMap((p) => p.leaves)
      : payrolls.payrolls
          .filter((p) => p.id === selectedPayroll?.workTimeId)
          .flatMap((p) => p.leaves);

    if (leaves.length === 0) return 0;
    const employeeLeaves = leaves.filter(
      (leave) =>
        !leave.isHospital &&
        (leave.status === AbsenceStatus.Approved ||
          leave.status === AbsenceStatus.EditedByAdmin)
    );

    const usedDays = calculateUniqueLeaveDays(
      employeeLeaves,
      new Date(selectedYear, selectedMonth, 1),
      new Date(selectedYear, selectedMonth + 1, 0),
      holidays
    );
    return usedDays;
  }, [
    selectedEmployee,
    selectedPayroll,
    selectedMonth,
    selectedYear,
    payrolls.payrolls,
    showMyAbsences,
    holidays,
  ]);

  const usedHospitalLeaveThisMonth = useMemo(() => {
    var leaves = selectedEmployee
      ? selectedEmployee.payrolls.flatMap((p) => p.leaves)
      : payrolls.payrolls
          .filter((p) => p.id === selectedPayroll?.workTimeId)
          .flatMap((p) => p.leaves);
    if (leaves.length === 0) return 0;
    const employeeLeaves = leaves.filter(
      (leave) =>
        leave.isHospital &&
        (leave.status === AbsenceStatus.Approved ||
          leave.status === AbsenceStatus.EditedByAdmin)
    );

    const usedDays = calculateUniqueLeaveDays(
      employeeLeaves,
      new Date(selectedYear, selectedMonth, 1),
      new Date(selectedYear, selectedMonth + 1, 0),
      holidays
    );

    return usedDays;
  }, [
    selectedEmployee,
    selectedPayroll,
    selectedMonth,
    selectedYear,
    payrolls.payrolls,
    showMyAbsences,
    holidays,
  ]);

  const filteredEmployees = useFilteredEmployees(
    payrolls.payrolls,
    selectedMonth,
    selectedYear,
    selectedPayroll
  );

  const monthBounds = useMemo(() => {
    const first = new Date(selectedYear, selectedMonth, 1);
    const last = new Date(selectedYear, selectedMonth + 1, 0);
    first.setHours(0, 0, 0, 0);
    last.setHours(0, 0, 0, 0);
    return { first, last };
  }, [selectedMonth, selectedYear]);

  const visibleMonthLeavesData = useMemo(() => {
    const allLeaves = filteredEmployees.flatMap((employee) =>
      employee.payrolls.flatMap((p) =>
        p.leaves.map((leave) => ({ leave, employee }))
      )
    );

    const visibleMonthLeaves = allLeaves.filter(({ leave }) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);
      leaveStart.setHours(0, 0, 0, 0);
      leaveEnd.setHours(0, 0, 0, 0);
      return leaveStart <= monthBounds.last && leaveEnd >= monthBounds.first;
    });

    const sortedLeaves = [...visibleMonthLeaves].sort(
      (a, b) =>
        new Date(a.leave.fromDate).getTime() -
        new Date(b.leave.fromDate).getTime()
    );

    const leaveRowAssignments = new Map<string, number>();
    const rowEndTimes: number[] = [];
    sortedLeaves.forEach(({ leave }) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);
      leaveStart.setHours(0, 0, 0, 0);
      leaveEnd.setHours(0, 0, 0, 0);
      const startMs = leaveStart.getTime();
      const endMs = leaveEnd.getTime();

      let assignedRowIndex = -1;
      for (let i = 0; i < rowEndTimes.length; i++) {
        if (rowEndTimes[i] < startMs) {
          assignedRowIndex = i;
          rowEndTimes[i] = endMs;
          break;
        }
      }
      if (assignedRowIndex === -1) {
        rowEndTimes.push(endMs);
        assignedRowIndex = rowEndTimes.length - 1;
      }
      leaveRowAssignments.set(leave.id ?? "", assignedRowIndex + 1);
    });

    return { sortedLeaves, leaveRowAssignments };
  }, [filteredEmployees, monthBounds.first, monthBounds.last]);

  const createDateMap = (days: DayInfo[]) => {
    const dateToIndex: { [dateStr: string]: number } = {};
    let firstDate: Date | null = null;
    let lastDate: Date | null = null;
    const dayKey = (d: Date) =>
      `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(
        d.getDate()
      ).padStart(2, "0")}`;

    days.forEach((dayInfo, index) => {
      const currMonth = findMonthNumber(dayInfo.type);
      const d = new Date(selectedYear, currMonth, dayInfo.dayNumber);
      d.setHours(0, 0, 0, 0);
      if (firstDate === null || d < firstDate) firstDate = d;
      if (lastDate === null || d > lastDate) lastDate = d;
      dateToIndex[dayKey(d)] = index;
    });

    return { dateToIndex, firstDate: firstDate!, lastDate: lastDate! };
  };

  const recomputeCalendar = (
    baseDays: DayInfo[]
  ): { updatedDays: DayInfo[]; weekMax: { [week: number]: number } } => {
    const updatedDays = baseDays.map((d) => ({
      ...d,
      missingEmployees: [] as AbsenceInfo[],
    }));
    const weekMaxRows: { [week: number]: number } = {};
    const { dateToIndex, firstDate, lastDate } = createDateMap(baseDays);

    const dayKey = (d: Date) =>
      `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(
        d.getDate()
      ).padStart(2, "0")}`;

    const { sortedLeaves, leaveRowAssignments } = visibleMonthLeavesData;

    sortedLeaves.forEach(({ leave, employee }) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);
      leaveStart.setHours(0, 0, 0, 0);
      leaveEnd.setHours(0, 0, 0, 0);

      const start = leaveStart > firstDate ? leaveStart : firstDate;
      const end = leaveEnd < lastDate ? leaveEnd : lastDate;

      for (
        let dayDate = new Date(start.getTime());
        dayDate.getTime() <= end.getTime();
        dayDate.setDate(dayDate.getDate() + 1)
      ) {
        const idx = dateToIndex[dayKey(dayDate)];
        if (idx === undefined) continue;

        const weekNumber = Math.floor(idx / 7);
        const finalRowPosition = leaveRowAssignments.get(leave.id ?? "") ?? 1;
        weekMaxRows[weekNumber] = Math.max(
          weekMaxRows[weekNumber] || 0,
          finalRowPosition + 1
        );

        let positionAlignment: AlignmentPosition = AlignmentPosition.Center;
        if (
          dayDate.getTime() === leaveStart.getTime() &&
          dayDate.getTime() === leaveEnd.getTime()
        ) {
          positionAlignment = AlignmentPosition.Both;
        } else if (dayDate.getTime() === leaveStart.getTime()) {
          positionAlignment = AlignmentPosition.Left;
        } else if (dayDate.getTime() === leaveEnd.getTime()) {
          positionAlignment = AlignmentPosition.Right;
        }

        updatedDays[idx].missingEmployees = [
          ...updatedDays[idx].missingEmployees,
          {
            id: leave.id ?? "",
            payrollId: leave.payrollId,
            userId: employee.userId,
            employeeName: employee.name,
            row: finalRowPosition,
            positonRounding: positionAlignment,
            isHospital: leave.isHospital,
            status: leave.status,
            exportStatus: leave.exportStatus,
            isHighlighted: highlightedAbsenceId
              ? leave.id === highlightedAbsenceId
              : false,
            startDate: leave.fromDate,
            endDate: leave.toDate,
            isOverlapping: leave.isOverlapping,
            typeIdentifier: leave.typeIdentifier,
            comment: leave.isHospital
              ? leave.sickNote ?? ""
              : leave.reference ?? "",
            sickNote: leave.sickNote ?? "",
          },
        ];
      }
    });

    return { updatedDays, weekMax: weekMaxRows };
  };

  useEffect(() => {
    const newDays = calculateDays(selectedMonth, selectedYear);
    const { updatedDays, weekMax } = recomputeCalendar(newDays);
    setCombinedDays(updatedDays);
    setMaxRowsPerWeek(weekMax);
  }, [selectedMonth, selectedYear, filteredEmployees, highlightedAbsenceId]);

  const findMonthNumber = (type: string) => {
    let currMonthOffset: number;
    if (type === "prevMonth") currMonthOffset = -1;
    else if (type === "currentMonth") currMonthOffset = 0;
    else currMonthOffset = 1;
    return selectedMonth + currMonthOffset;
  };

  const createDayInfo = (dayNumber: number, type: string) => {
    const currMonth = findMonthNumber(type);
    const dayDate = new Date(selectedYear, currMonth, dayNumber);
    return {
      dayDate,
      dayNumber,
      type,
      missingEmployees: [] as AbsenceInfo[],
    };
  };

  const calculateDays = (selectedMonth: number, selectedYear: number) => {
    const daysInMonth = (month: number, year: number) =>
      new Date(year, month + 1, 0).getDate();

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1).getDay();
    const numDaysCurrentMonth = daysInMonth(selectedMonth, selectedYear);
    const adjustDayIndex = (dayIndex: number) =>
      dayIndex === 0 ? 7 : dayIndex;
    const prevMonthDisplayDays = adjustDayIndex(firstDayOfMonth) - 1;

    const prevMonthDays = Array.from(
      { length: prevMonthDisplayDays },
      (_, i) =>
        daysInMonth(selectedMonth - 1, selectedYear) -
        prevMonthDisplayDays +
        i +
        1
    );

    const currentMonthDays = Array.from(
      { length: numDaysCurrentMonth },
      (_, i) => i + 1
    );

    const totalDays = prevMonthDisplayDays + numDaysCurrentMonth;
    const nextMonthDisplayDays = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);

    const nextMonthDays = Array.from(
      { length: nextMonthDisplayDays },
      (_, i) => i + 1
    );

    return [
      ...prevMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "prevMonth")
      ),
      ...currentMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "currentMonth")
      ),
      ...nextMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "nextMonth")
      ),
    ] as DayInfo[];
  };

  const goToPrevMonth = () => {
    let newMonth = selectedMonth - 1;
    let newYear = selectedYear;
    if (newMonth < 0) {
      newMonth = 11;
      newYear -= 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToNextMonth = () => {
    let newMonth = selectedMonth + 1;
    let newYear = selectedYear;
    if (newMonth > 11) {
      newMonth = 0;
      newYear += 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const isCurrentDay = (day: number) => {
    return (
      selectedMonth === currentMonth &&
      selectedYear === currentYear &&
      day === currentDate
    );
  };

  const isHoliday = (day: number) => {
    if (!holidays) return false;

    const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(
      2,
      "0"
    )}-${String(day).padStart(2, "0")}`;

    return holidays.some((holiday) => holiday.date === dateStr);
  };

  return (
    <DatesTableView
      data-testid="dates-table-view"
      selectedMonth={selectedMonth}
      selectedYear={selectedYear}
      usedLeavesThisMonth={usedLeaveThisMonth ?? 0}
      usedHospitalLeavesThisMonth={usedHospitalLeaveThisMonth ?? 0}
      days={combinedDays}
      maxRowsPerWeek={maxRowsPerWeek}
      onPrevMonth={goToPrevMonth}
      onNextMonth={goToNextMonth}
      isCurrentDay={isCurrentDay}
      isHoliday={isHoliday}
      holidaysCurrentMonth={holidays}
      setSelectedPayroll={setSelectedPayroll}
      selectedPayroll={selectedPayroll}
      selectedEmployee={selectedEmployee}
      hoveredEmployee={hoveredEmployee}
      showMyAbsences={showMyAbsences}
    />
  );
};

export default DatesTableContainer;
