﻿using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Nomenclatures;

namespace WorkTimeApi.Common.DTOs.Payrolls
{
    public class PayrollSummaryDTO
    {
       public string? ContractNumber { get; set; }
        
        public Guid StructureLevelId { get; set; }

        public string? StructureLevelName { get; set; }
        
        public NomenclatureDTO? Position { get; set; }
        
        public NomenclatureDTO? ContractType { get; set; }

        public NomenclatureDTO? ContractReason { get; set; }

        public NomenclatureDTO? IncomeType { get; set; }

        public NomenclatureDTO? PermanentContractType { get; set; }

        public int? DailyWorktime { get; set; }
        
        public string? Workplace { get; set; }
        
        public DateTime? FromDate { get; set; }
        
        public DateTime? ToDate { get; set; }
                
        public DateTime? ContractTermDate { get; set; }
        
        public DateTime? ContractDate { get; set; }
        
        public DateTime? ContractEndDate { get; set; }
                
        public string? Kid { get; set; }
        
        public string? Ekatte { get; set; }
        
        public string? Nkpd { get; set; }        
        
        public LengthOfServiceDTO? ProfessionalЕxperienceInCompany { get; set; }
        
        public LengthOfServiceDTO? ProfessionalЕxperience { get; set; }
        
        public LengthOfServiceDTO? WorkExperience { get; set; }

        public List<AnnexPayrollSummary>? AnnexPayrolls { get; set; } = new List<AnnexPayrollSummary>();
    }
}
