import { useParams } from "react-router-dom";
import Translator from "../../services/language/Translator";
import EmailSentIcon from "../../assets/images/profile/email-sent-icon.svg";

const ConfirmationEmailSent = () => {
  const { email } = useParams();
  const decodedEmail = email ? decodeURIComponent(email) : "";
  return (
    <div
      style={{
        minHeight: "calc(100vh - 100px)",
        backgroundColor: "#f8f9fa",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          backgroundColor: "transparent",
          textAlign: "center",
          maxWidth: "500px",
          width: "100%",
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 1,
        }}
      >
        <div
          style={{
            marginBottom: "0.8rem",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <img
            src={EmailSentIcon}
            alt="Email sent"
            style={{
              width: "117px",
              height: "48px",
            }}
          />
        </div>

        <div
          style={{
            fontSize: "18px",
            lineHeight: "24px",
            fontWeight: "normal",
            letterSpacing: "0px",
            color: "var(--confirmation-email-sent-color)",
            opacity: "1",
          }}
        >
          <div
            style={{
              marginBottom: "0.5rem",
            }}
          >
            <Translator getString="Confirmation E-mail sent to" />
            <div
              style={{
                fontSize: "18px",
                lineHeight: "24px",
                fontWeight: "normal",
                letterSpacing: "0px",
                color: "var(--confirmation-email-sent-color)",
                opacity: "1",
              }}
            >
              {decodedEmail}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationEmailSent;
