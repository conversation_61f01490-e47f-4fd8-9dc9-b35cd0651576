{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"WorkTimeConnection": "Server=.,2022; Database=microinvest-worktimeapi; User Id=sa; Password=***********; MultipleActiveResultSets=true; TrustServerCertificate=True"}, "WorkTimeDefaultCompanyId": "B3007F68-D72E-48AB-A0B5-DEEE0EAD94B1", "SSOBaseUrl": "https://localhost:7041/", "EmailsApiBaseUrl": "http://*************/emails/", "WorkTimeUrl": "http://localhost:3000/", "GatewayUrl": "https://localhost:7056/"}