﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;Docker</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SeedData\**" />
    <EmbeddedResource Remove="SeedData\**" />
    <None Remove="SeedData\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Resources\DefaultLocationData\DefaultLocationData.xlsx" />
    <None Remove="Resources\SeedData\KID_2025.xlsx" />
    <None Remove="Resources\SeedData\NKPD_2023.xlsx" />
    <None Remove="Resources\SeedData\NKPD_2025.xlsx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\DefaultLocationData\DefaultLocationData.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\SeedData\KID_2008.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\SeedData\KID_2025.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\SeedData\NKPD_2023.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\SeedData\NKPD_2025.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Repositories\Templates\NewFolder\" />
  </ItemGroup>
	
  <ItemGroup>
  	<ProjectReference Include="..\WorkTimeApi.Common\WorkTimeApi.Common.csproj" />
  </ItemGroup>

</Project>
