using Gateway.Common.Extensions.LoggerExtensions;
using Gateway.Common.Middlewares;
using Gateway.Common.Middlewares.Security;
using Gateway.Common.Models;
using Gateway.Extenstions.EndpointExtensions;
using Microsoft.EntityFrameworkCore;
using WatchTower.Common.Extensions.Aspire;
using WatchTower.Common.Extensions.HealthCheck;
using WatchTower.Extenstions.MetricsExtensions;
using WorkTimeApi.Database;
using WorkTimeApi.Infrastructure.MediatR;
using WorkTimeApi.Extensions;
using WorkTimeApi.Infrastructure.Configuration;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

builder.Services.AddEndpointDefinitions(typeof(Program));
builder.Services.InitializeAutoMapper();

builder.Services.AddMediatR(configuration =>
{
    configuration.Lifetime = ServiceLifetime.Scoped;
    configuration.RegisterGenericHandlers = false;
    configuration.RegisterServicesFromAssembly(typeof(Program).Assembly);
    configuration.NotificationPublisherType = typeof(BackgroundNotificationPublisher);
});

builder.Services.AddDbContextFactory<WorkTimeApiDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("WorkTimeConnection")));

builder.Services.AddCors(p => p.AddPolicy("corsapp", builder =>
{
    builder
        .SetIsOriginAllowed(_ => true)
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
}));

builder.Services.AddOpenTelemetryMetrics();
builder.Services.AddOpenSearchLogging();

var originSettings = builder.Configuration.GetSection("OriginSettings").Get<OriginSettings>() ?? new();
builder.Services.Configure<HolidayOptions>(builder.Configuration.GetSection("Holidays"));
builder.Services.AddSingleton(originSettings);

builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck<WorkTimeApiDbContext>>("Database");

var app = builder.Build();

app.MapDefaultEndpoints();

app.MapPrometheusScrapingEndpoint();
app.UseEndpointDefinitions();
app.MigrateDatabase();
app.UseCors("corsapp");
app.UseMiddlewareExceptionLogging();
app.UseMiddleware<RequestIdMiddleware>()
    .UseMiddleware<UserIdMiddleware>();

app.UseHealthChecks();

app.Run();
