﻿using System.Linq.Expressions;
using WorkTimeApi.Database.Models;

namespace WorkTimeApi.Database.Repositories.Interfaces.Absences
{
    public interface IAbsencesRepository
    {
        Task<List<T>> AddAbsencesAsync<T>(List<T> leaves) where T : BaseAbsence;

        Task<T> AddAbsenceAsync<T>(T absence) where T : BaseAbsence;

        Task<IEnumerable<T>> GetAbsencesForMonthAsync<T>(Guid companyId, DateTime date) where T : BaseAbsence;

        Task<IEnumerable<T>> GetEmployeeAbsencesAsync<T>(Guid employeeId) where T : BaseAbsence;

        Task<IEnumerable<T>> GetPayrollAbsenceAsync<T>(Guid payrollId, Expression<Func<T, bool>> predicate) where T : BaseAbsence;

        Task<T> DeletePersonalAbsenceAsync<T>(Guid absenceId, Guid payrollId, Guid userId) where T : BaseAbsence;

        Task<T> DeleteAbsenceAsync<T>(Guid absenceId) where T : BaseAbsence;

        Task<T> UpdateAbsenceAsync<T>(T absence) where T : BaseAbsence;

        Task<T?> GetByIdAsync<T>(Guid absenceId) where T : BaseAbsence;

        Task<T?> GetByIdForUpdateAsync<T>(Guid absenceId) where T : BaseAbsence;

        Task<Employee?> GetEmployeeByAbsenceIdAsync<T>(Guid absenceId) where T : BaseAbsence;

        Task<List<T>> UpdateOverlapingAbsences<T>(Guid hospitalId) where T : BaseAbsence;

        Task<IEnumerable<T>> GetAbsencesForCurrentPayrollsAsync<T>(List<Guid> payrollIds) where T : BaseAbsence;

        Task<IEnumerable<T>> GetCompanyAbsencesForTRZAsync<T>(Guid companyId, DateTime month, bool includeExporte) where T : BaseAbsence;

        Task<IEnumerable<T>> MarkExportedAsync<T>(Guid companyId, IEnumerable<Guid> ids) where T : BaseAbsence;
    }
}
