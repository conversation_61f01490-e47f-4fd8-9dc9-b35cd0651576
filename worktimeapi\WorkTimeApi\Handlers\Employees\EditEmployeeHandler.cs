﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Common.Notifications.SignalR;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class EditEmployeeHandler(IEmployeesService _employeesService,
        IMediator mediator,
        GlobalEmployee globalEmployee,
        GlobalUser globalUser) : IRequestHandler<EditEmployeeRequest, IResult>
    {
        public async Task<IResult> Handle(EditEmployeeRequest request, CancellationToken cancellationToken)
        {
            var isEditorAdmin = globalEmployee.Permissions.Contains(DefaultPermissions.Employees.Write);
            var result = await _employeesService.EditEmployeeAsync(request, globalEmployee.Id, isEditorAdmin);

            var hasPersonalDataChanges = request.IdentityCardData is not null || request.PersonalData is not null;

            INotification notification = (isEditorAdmin, hasPersonalDataChanges) switch
            {
                (true, true) => new EditEmployeeByAdminNotification(result, request.PayrollId, globalEmployee.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}"),
                (true, false) => new EditEmployeeAddressesByAdminNotification(result, request.PayrollId, globalEmployee.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}"),
                (false, true) => new EditEmployeeByEmployeeNotification(result, request.PayrollId, globalEmployee.CompanyId, globalUser.Id, $"{result.FirstName} {result.LastName}"),
                (false, false) => new EditEmployeeAddressesByEmployeeNotification(result, request.PayrollId, globalEmployee.CompanyId, globalUser.Id, $"{result.FirstName} {result.LastName}"),
            };

            mediator.Publish(notification, cancellationToken);

            var employeeUpdatedNotification = new EmployeeUpdatedNotification(
                new EmployeeUpdatedPayload
                {
                    EmployeeId = result.WorkTimeId,
                    PayrollId = request.PayrollId
                },
                globalEmployee.CompanyId,
                globalUser.Id,
                $"{globalUser.FirstName} {globalUser.LastName}"
            );
            mediator.Publish(employeeUpdatedNotification, cancellationToken);

            return Results.Ok(result);
        }
    }
}
