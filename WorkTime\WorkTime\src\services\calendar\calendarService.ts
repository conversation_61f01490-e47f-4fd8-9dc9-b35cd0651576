import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";
import { HolidayDTO } from "../../models/DTOs/Holidays/HolidaysDTO";

const countWorkingDays = (
  startDate: Date,
  endDate: Date,
  isWorkingDay: (date: Date) => boolean
): number => {
  let workingDays = 0;
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    if (isWorkingDay(currentDate)) {
      workingDays++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return workingDays;
};


export const calculateWorkingDaysWithHolidays = (
  startDate: Date,
  endDate: Date,
  holidays: HolidayDTO[] = []
): number => {
  return countWorkingDays(startDate, endDate, (date) => {
    const dayOfWeek = date.getDay();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const isHoliday = holidays.some((holiday) => holiday.date === dateString);
    return !isWeekend && !isHoliday;
  });
};

export const calculateDaysInSelectedYear = (
  leave: AbsenceHospitalDTO,
  selectedYear: string,
  holidays: HolidayDTO[] = []
): number => {
  const selectedYearNum = parseInt(selectedYear);
  const fromDate = new Date(leave.fromDate);
  const toDate = new Date(leave.toDate);
  const yearStart = new Date(selectedYearNum, 0, 1);
  const yearEnd = new Date(selectedYearNum, 11, 31);

  if (fromDate > yearStart && toDate < yearEnd && leave.duration) {
    return leave.duration;
  }

  const effectiveStartDate = fromDate < yearStart ? yearStart : fromDate;
  const effectiveEndDate = toDate > yearEnd ? yearEnd : toDate;

  if (effectiveStartDate > yearEnd || effectiveEndDate < yearStart) {
    return 0;
  }

  return calculateWorkingDaysWithHolidays(effectiveStartDate, effectiveEndDate, holidays);
};

export const calculateLeaveDaysInPeriod = (
  leave: AbsenceHospitalDTO,
  startDate: Date,
  endDate: Date,
  holidays: HolidayDTO[]
): number => {
  const fromDate = new Date(leave.fromDate);
  const toDate = new Date(leave.toDate);
  const yearStart = new Date(startDate);
  const yearEnd = new Date(endDate);

  if (fromDate > yearStart && toDate < yearEnd && leave.duration) {
    return leave.duration ;
  }

  const effectiveStartDate = fromDate < yearStart ? yearStart : fromDate;
  const effectiveEndDate = toDate > yearEnd ? yearEnd : toDate;

  if (effectiveStartDate > yearEnd || effectiveEndDate < yearStart) {
    return 0;
  }
  
  return calculateWorkingDaysWithHolidays(effectiveStartDate, effectiveEndDate, holidays);
};

export const getMonthWorkingDays = (month: number, year: number, nonWorkingOfficialHolidays: HolidayDTO[]): number => {
  const startDate = new Date(year, month, 1);
  const endDate = new Date(year, month + 1, 0);
  return calculateWorkingDaysWithHolidays(startDate, endDate, nonWorkingOfficialHolidays);
};

export const calculateDaysWorkedWithPayroll = (
  selectedMonth: number,
  selectedYear: number,
  payrollFromDate: string,
  payrollToDate: string | undefined,
  nonWorkingOfficialHolidays: HolidayDTO[],
  usedLeavesThisMonth: number,
  usedHospitalLeavesThisMonth: number,
): number => {
  const payrollStart = new Date(payrollFromDate);
  const monthStart = new Date(selectedYear, selectedMonth, 1);
  const monthEnd = new Date(selectedYear, selectedMonth + 1, 0); 
  
  let effectiveStartDate: Date;
  let effectiveEndDate: Date;
  
  if (payrollStart <= monthEnd) {
    effectiveStartDate = payrollStart > monthStart ? payrollStart : monthStart;
  } else {
    return 0;
  }
  
  if (payrollToDate) {
    const lastWorkingDay = new Date(payrollToDate);
    lastWorkingDay.setDate(lastWorkingDay.getDate());
    
    if (lastWorkingDay >= monthStart) {
      effectiveEndDate = lastWorkingDay < monthEnd ? lastWorkingDay : monthEnd;
    } else {
      return 0;
    }
  } else {
    effectiveEndDate = monthEnd;
  }

  if (effectiveStartDate > effectiveEndDate) {
    return 0;
  }
  
  const workingDaysInPeriod = calculateWorkingDaysWithHolidays(
    effectiveStartDate,
    effectiveEndDate,
    nonWorkingOfficialHolidays
  );
  
  return workingDaysInPeriod - (usedHospitalLeavesThisMonth + usedLeavesThisMonth);
};

export const calculateUniqueLeaveDays = (
  leaves: AbsenceHospitalDTO[],
  periodStart: Date,
  periodEnd: Date,
  holidays: HolidayDTO[]
): number => {
  if (leaves.length === 0) return 0;

  const coveredDates = new Set<string>();

  leaves.forEach((leave) => {
    const leaveStart = new Date(
      Math.max(new Date(leave.fromDate).getTime(), periodStart.getTime())
    );
    const leaveEnd = new Date(
      Math.min(new Date(leave.toDate).getTime(), periodEnd.getTime())
    );

    if (leaveStart <= leaveEnd) {
      const currentDate = new Date(leaveStart);
      while (currentDate <= leaveEnd) {
        const dayOfWeek = currentDate.getDay();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const dateString = `${year}-${month}-${day}`;
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        const isHoliday = holidays.some((holiday) => holiday.date === dateString);
        
        if (!isWeekend && !isHoliday) {
          coveredDates.add(dateString);
        }
        
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
  });

  return coveredDates.size;
};


