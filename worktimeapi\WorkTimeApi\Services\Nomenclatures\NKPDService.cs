﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Database.Repositories.Interfaces.NKPDs;
using WorkTimeApi.Services.Interfaces.Nomenclatures;

namespace WorkTimeApi.Services.Nomenclatures
{
    public class NKPDService : INKPDService
    {
        private readonly INKPDsRepository _nkpdRepository;
        private readonly IMapper _mapper;

        public NKPDService(INKPDsRepository nkpdsRepository, IMapper mapper)
        {
            _nkpdRepository = nkpdsRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<NKPDDTO>> GetNKPDsAsync()
        {
            var nkpds = _mapper.Map<IEnumerable<NKPDDTO>>(await _nkpdRepository.GetNKPDsAsync());

            return nkpds;
        }

        public async Task<KpdDTO?> GetKpdAsync(string section, string code, DateTime validFromDate)
        {
            var kpd = await _nkpdRepository.GetKpd(section, code, validFromDate);
            if (kpd == null)
                return null;

            var kpdDTO = _mapper.Map<KpdDTO>(kpd);

            return kpdDTO;
        }
    }
}
