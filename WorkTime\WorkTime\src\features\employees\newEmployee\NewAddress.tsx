import { useEffect, useState } from "react";
import styled from "styled-components";
import addressDot from "../../../assets/images/dot-icons/addressDot.svg";
import mailDot from "../../../assets/images/dot-icons/mailDot.svg";
import phoneDot from "../../../assets/images/dot-icons/phoneDot.svg";
import Container from "../../../components/Container";
import Dropdown from "../../../components/Dropdown/Dropdown";
import {
  DropdownContainer,
  DropdownFormField,
  DropdownFormRow,
  HeaderImage,
  HeaderWrapper,
  DropdownBody as StyledDropdownBody,
  StyledInput,
  Wrapper,
} from "../../../components/Dropdown/DropdownStyles";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";
import { PropertyEditFieldWrapper } from "../../../components/PropertyEdit/PropertyEditFieldWrapper";
import { usePropertyEdits } from "../../../hooks/usePropertyEdits";
import { AddressPurpose } from "../../../models/DTOs/address/AddressDTO";
import { EmployeePropertyEditDTO } from "../../../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import { IEnumType } from "../../../models/DTOs/enums/IEnumType";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import Translator, { translate } from "../../../services/language/Translator";
import { useDefaultPlaces } from "../../DefaultLocationDataContext";
import { useMenu } from "../../MenuContext";

const FormContainer = styled(Container)<{ $isNewEmployeeFlow?: boolean }>`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: ${(props) =>
    props.$isNewEmployeeFlow ? "calc(100vh - 280px)" : "calc(100vh - 250px)"};
  overflow: auto;
  padding-right: 0.8rem;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;

  &:first-child {
    margin-top: 0;
  }
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  color: #333;
  white-space: nowrap;
  margin-left: 0.5rem;
`;

const SectionSeparator = styled.div`
  height: 1px;
  display: flex;
  background-color: white;
  width: 100%;
  margin-left: 0.5rem;
  margin-top: 0.3rem;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 0.3rem;
  width: 100%;
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const SingleFieldRow = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
`;

const DropdownBody = styled(StyledDropdownBody).attrs({ as: Dropdown.Body })`
  background: var(--textbox-color);
`;

const SaveButtonContainer = styled(Container)`
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  border-top: 1px solid #e0e0e0;
  position: absolute;
  bottom: 0;
  z-index: 5;
  width: 90%;
`;

const SaveButton = styled(Button)`
  width: 100%;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: NewAddressDTO;
  onChange?: () => void;
  onSave?: () => void;
  isNewEmployeeFlow?: boolean;
  propertyEdits?: EmployeePropertyEditDTO[];
  shouldShowAdminEditButtons?: boolean;
  payrollId?: string;
  employeeId?: string;
  addressId?: string;
  onPropertyEditsUpdate?: (updatedEdits: EmployeePropertyEditDTO[]) => void;
}

const NewAddress = ({
  onValidation,
  data,
  onChange,
  onSave,
  isNewEmployeeFlow,
  propertyEdits = [],
  shouldShowAdminEditButtons = false,
  payrollId,
  employeeId,
  addressId,
  onPropertyEditsUpdate,
}: Props) => {
  const { cities, districts, municipalities, countries } = useDefaultPlaces();
  const { viewData } = useMenu();

  const [formData, setFormData] = useState<NewAddressDTO>({
    id: data?.id || undefined,
    employeeId: viewData?.employeeId || undefined,
    city: data?.city || null,
    postalCode: data?.postalCode || "",
    municipality: data?.municipality || null,
    district: data?.district || null,
    street: data?.street || "",
    block: data?.block || "",
    apartment: data?.apartment || "",
    phone: data?.phone || "",
    workPhone: data?.workPhone || "",
    email: data?.email || "",
    workEmail: data?.workEmail || "",
    country: data?.country || null,
    purpose: data?.purpose ?? AddressPurpose.ForContact,
    description: data?.description || "",
    neighborhood: data?.neighborhood || "",
    cityName: data?.cityName || "",
    districtName: data?.districtName || "",
    municipalityName: data?.municipalityName || "",
    countryName: data?.countryName || "",
  });

  const { getPropertyEditInfo, handleApproveProperty, handleDeclineProperty } =
    usePropertyEdits({
      propertyEdits: shouldShowAdminEditButtons ? propertyEdits : [],
      shouldShowAdminEditButtons,
      payrollId,
      employeeId,
      onPropertyEditsUpdate,
      tabName: translate("Addresses"),
    });

  useEffect(() => {
    if (data) {
      setFormData((prev) => ({
        ...prev,
        id: data.id || prev.id,
        employeeId: viewData?.employeeId || prev.employeeId,
        city: data.purpose === AddressPurpose.Abroad ? null : data.city || null,
        postalCode: data.postalCode || prev.postalCode,
        municipality:
          data.purpose === AddressPurpose.Abroad
            ? null
            : data.municipality || null,
        district:
          data.purpose === AddressPurpose.Abroad ? null : data.district || null,
        street: data.street || prev.street,
        block: data.block || prev.block,
        apartment: data.apartment || prev.apartment,
        phone: data.phone || prev.phone,
        workPhone: data.workPhone || prev.workPhone,
        email: data.email || prev.email,
        workEmail: data.workEmail || prev.workEmail,
        country: data.country || prev.country,
        purpose: data.purpose ?? prev.purpose,
        description: data.description || prev.description,
        neighborhood: data.neighborhood || prev.neighborhood,
        cityName: data.cityName || prev.cityName,
        districtName: data.districtName || prev.districtName,
        municipalityName: data.municipalityName || prev.municipalityName,
        countryName: data.countryName || prev.countryName,
      }));

      if (data.purpose === AddressPurpose.Abroad) {
        setCitySearchText("");
        setDistrictSearchText("");
        setMunicipalitySearchText("");
      }
    }
  }, [data, viewData?.employeeId]);

  useEffect(() => {
    if (countries.length > 0 && formData.country && !formData.countryName) {
      const country = countries.find(
        (country) => country.identifier === formData.country?.identifier
      );
      if (country) {
        setFormData((prev) => ({
          ...prev,
          countryName: country.name,
        }));
      }
    }
  }, [countries, formData.country, formData.countryName]);

  const postCodes = Array.from(
    new Set(
      cities
        .map((city) => city.postCode)
        .filter((postCode) => postCode !== undefined)
    )
  );

  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [isDistrictDropdownOpen, setIsDistrictDropdownOpen] = useState(false);
  const [isMunicipalityDropdownOpen, setIsMunicipalityDropdownOpen] =
    useState(false);
  const [isPostalCodeDropdownOpen, setIsPostalCodeDropdownOpen] =
    useState(false);
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
  const [citySearchText, setCitySearchText] = useState("");
  const [districtSearchText, setDistrictSearchText] = useState("");
  const [municipalitySearchText, setMunicipalitySearchText] = useState("");
  const [postalCodeSearchText, setPostalCodeSearchText] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value,
    };

    setFormData(newFormData);
    onValidation?.(true, newFormData);
    if (onChange) {
      onChange();
    }
  };

  useEffect(() => {
    const isValid = true;

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, []);

  const handleCityDropdownToggle = (isOpen: boolean) => {
    setIsCityDropdownOpen(isOpen);
  };

  const handleDistrictDropdownToggle = (isOpen: boolean) => {
    setIsDistrictDropdownOpen(isOpen);
  };

  const handleMunicipalityDropdownToggle = (isOpen: boolean) => {
    setIsMunicipalityDropdownOpen(isOpen);
  };

  const handleCityTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCitySearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      city: null,
      cityName: e.target.value,
    }));
    setIsCityDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleDistrictTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDistrictSearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      district: null,
      districtName: e.target.value,
    }));
    setIsDistrictDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleMunicipalityTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setMunicipalitySearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      municipality: null,
      municipalityName: e.target.value,
    }));
    setIsMunicipalityDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleCitySelect = (city: any) => {
    setCitySearchText(city.name);

    const matchingMunicipality = municipalities.find(
      (municipality) => municipality.id === city.municipalityId
    );

    const matchingDistrict = districts.find(
      (district) => district.id === matchingMunicipality?.districtId
    );

    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        postalCode:
          cities
            .filter((c) => c.name === city.name)
            .sort((a, b) =>
              (a.postCode || "").localeCompare(b.postCode || "")
            )[0]?.postCode || prev.postalCode,
        city: city,
        cityName: city.name,
        ...(matchingDistrict && {
          district: matchingDistrict,
          districtName: matchingDistrict.name,
        }),
        ...(matchingMunicipality && {
          municipality: matchingMunicipality,
          municipalityName: matchingMunicipality.name,
        }),
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    setIsCityDropdownOpen(false);
    if (onChange) {
      onChange();
    }
  };

  const handleDistrictSelect = (district: any) => {
    setDistrictSearchText(district.name);
    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        district: district,
        districtName: district.name,
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    if (onChange) {
      onChange();
    }

    setIsDistrictDropdownOpen(false);
  };

  const handleMunicipalitySelect = (municipality: any) => {
    setMunicipalitySearchText(municipality.name);
    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        municipality: municipality,
        municipalityName: municipality.name,
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    if (onChange) {
      onChange();
    }

    setIsMunicipalityDropdownOpen(false);
  };

  const filteredCities = cities
    .filter(
      (city) =>
        city.postCode &&
        city.postCode !== "" &&
        (!formData.municipality ||
          formData.municipality?.id === city.municipalityId) &&
        city.name.toLowerCase().startsWith(citySearchText.toLowerCase())
    )
    .filter(
      (city, index, array) =>
        array.findIndex((c) => c.name === city.name) === index
    )
    .sort((a, b) => {
      const municipalityName = formData?.municipality?.name?.toLowerCase();
      const aIsMatch = a.name.toLowerCase() === municipalityName;
      const bIsMatch = b.name.toLowerCase() === municipalityName;

      return (
        (bIsMatch ? 1 : 0) - (aIsMatch ? 1 : 0) || a.name.localeCompare(b.name)
      );
    });

  const filteredCountries = countries.sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  const filteredDistricts = districts
    .filter((district) =>
      district.name.toLowerCase().startsWith(districtSearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const filteredMunicipalities = municipalities
    .filter(
      (municipality) =>
        (!formData.district ||
          formData.district?.id === municipality.districtId) &&
        municipality.name
          .toLowerCase()
          .startsWith(municipalitySearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const filteredPostCodes = postCodes
    .filter((postCode) => postCode?.includes(postalCodeSearchText))
    .sort((a, b) => a.localeCompare(b));

  const handlePostalCodeDropdownToggle = (isOpen: boolean) => {
    setIsPostalCodeDropdownOpen(isOpen);
  };

  const handlePostalCodeTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPostalCodeSearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      postalCode: e.target.value,
    }));
    setIsPostalCodeDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handlePostalCodeSelect = (postCode: string) => {
    setPostalCodeSearchText(postCode);

    let matchingCity = cities.find(
      (city) =>
        city.postCode === postCode &&
        city.name.toLowerCase() ===
          municipalities
            .find((municipality) => municipality.id === city.municipalityId)
            ?.name?.toLowerCase()
    );

    if (!matchingCity) {
      matchingCity = cities.find((city) => city.postCode === postCode);
    }

    if (matchingCity) {
      const matchingMunicipality = municipalities.find(
        (municipality) => municipality.id === matchingCity.municipalityId
      );

      const matchingDistrict = districts.find(
        (district) => district.id === matchingMunicipality?.districtId
      );

      setFormData((prev: NewAddressDTO) => {
        const newFormData = {
          ...prev,
          postalCode: postCode,
          city: matchingCity,
          cityName: matchingCity.name,
          ...(matchingDistrict && {
            district: matchingDistrict,
            districtName: matchingDistrict.name,
          }),
          ...(matchingMunicipality && {
            municipality: matchingMunicipality,
            municipalityName: matchingMunicipality.name,
          }),
        };
        if (onValidation) {
          onValidation(true, newFormData);
        }
        return newFormData;
      });
    } else {
      setFormData((prev: NewAddressDTO) => {
        const newFormData = {
          ...prev,
          postalCode: postCode,
        };
        if (onValidation) {
          onValidation(true, newFormData);
        }
        return newFormData;
      });
    }

    setIsPostalCodeDropdownOpen(false);
    if (onChange) {
      onChange();
    }
  };

  const handleSave = async () => {
    if (onSave) {
      onSave();
    }
  };
  const handleCountrySelect = (country: IEnumType) => {
    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        country: country,
        countryName: country.name,
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });
    setIsCountryDropdownOpen(false);
    if (onChange) {
      onChange();
    }
  };
  const handleCountryDropdownToggle = (isOpen: boolean) => {
    setIsCountryDropdownOpen(isOpen);
  };

  return (
    <FormContainer $isNewEmployeeFlow={isNewEmployeeFlow}>
      <SectionHeader>
        <Image src={addressDot} data-testid="address-dot-image" />
        <SectionTitle>
          {formData.purpose === AddressPurpose.Custom
            ? translate("New address")
            : translate("Address")}
        </SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      {!isNewEmployeeFlow && formData.purpose === AddressPurpose.Custom && (
        <SingleFieldRow>
          <FormField>
            {(() => {
              const propertyEditInfo = getPropertyEditInfo(
                "Address",
                addressId || "",
                "Description"
              );
              return (
                <PropertyEditFieldWrapper
                  propertyEditInfo={propertyEditInfo}
                  onApprove={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleApproveProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                  onDecline={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleDeclineProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                >
                  <Textbox
                    name="description"
                    label={translate("Address name")}
                    value={
                      propertyEditInfo.hasEdit
                        ? propertyEditInfo.oldValue || undefined
                        : formData.description
                    }
                    handleChange={handleChange}
                    readonly={propertyEditInfo.isReadonly}
                  />
                </PropertyEditFieldWrapper>
              );
            })()}
          </FormField>
        </SingleFieldRow>
      )}

      <DropdownFormRow>
        <DropdownFormField>
          {formData.purpose === AddressPurpose.Abroad
            ? (() => {
                const propertyEditInfo = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "Country"
                );
                return (
                  <PropertyEditFieldWrapper
                    propertyEditInfo={propertyEditInfo}
                    onApprove={
                      propertyEditInfo.pendingEdit
                        ? () =>
                            handleApproveProperty(
                              propertyEditInfo.pendingEdit!.id
                            )
                        : undefined
                    }
                    onDecline={
                      propertyEditInfo.pendingEdit
                        ? () =>
                            handleDeclineProperty(
                              propertyEditInfo.pendingEdit!.id
                            )
                        : undefined
                    }
                  >
                    <DropdownContainer>
                      <Dropdown
                        isOpened={
                          !propertyEditInfo.isReadonly
                            ? handleCountryDropdownToggle
                            : undefined
                        }
                        isOpen={isCountryDropdownOpen}
                        data-testid="country-dropdown"
                      >
                        <Dropdown.Header data-testid="country-dropdown-header">
                          <HeaderWrapper
                            isOpen={isCountryDropdownOpen}
                            data-testid="country-header-wrapper"
                          >
                            {formData.countryName ? (
                              <Translator getString={formData.countryName} />
                            ) : (
                              <span style={{ color: "#999" }}>
                                {translate("Country")}
                              </span>
                            )}
                          </HeaderWrapper>
                          <HeaderImage
                            isClicked={isCountryDropdownOpen}
                            data-testid="country-header-image"
                          ></HeaderImage>
                        </Dropdown.Header>
                        {!propertyEditInfo.isReadonly && (
                          <DropdownBody data-testid="country-dropdown-body">
                            {filteredCountries.map((country) => (
                              <Wrapper
                                isOpen={isCountryDropdownOpen}
                                key={country.name}
                                onClick={() => handleCountrySelect(country)}
                                data-testid={`country-wrapper-${country.name}`}
                              >
                                <Translator getString={country.name} />
                              </Wrapper>
                            ))}
                          </DropdownBody>
                        )}
                      </Dropdown>
                    </DropdownContainer>
                  </PropertyEditFieldWrapper>
                );
              })()
            : (() => {
                const propertyEditInfoName = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "CityName"
                );
                const propertyEditInfo = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "CityId"
                );
                if (propertyEditInfo) {
                  propertyEditInfo.newValue =
                    cities.find((c) => c.id === propertyEditInfo.newValue)
                      ?.name ?? "";
                  propertyEditInfo.oldValue =
                    cities.find((c) => c.id === propertyEditInfo.oldValue)
                      ?.name ?? "";
                }

                return (
                  <PropertyEditFieldWrapper
                    propertyEditInfo={propertyEditInfo ?? propertyEditInfoName}
                    onApprove={
                      propertyEditInfo.pendingEdit ||
                      propertyEditInfoName.pendingEdit
                        ? async () => {
                            if (propertyEditInfoName.pendingEdit) {
                              await handleApproveProperty(
                                propertyEditInfoName.pendingEdit.id
                              );
                            }
                            if (propertyEditInfo.pendingEdit) {
                              await handleApproveProperty(
                                propertyEditInfo.pendingEdit.id
                              );
                            }
                          }
                        : undefined
                    }
                    onDecline={
                      propertyEditInfo.pendingEdit ||
                      propertyEditInfoName.pendingEdit
                        ? async () => {
                            if (propertyEditInfoName.pendingEdit) {
                              await handleDeclineProperty(
                                propertyEditInfoName.pendingEdit.id
                              );
                            }
                            if (propertyEditInfo.pendingEdit) {
                              await handleDeclineProperty(
                                propertyEditInfo.pendingEdit.id
                              );
                            }
                          }
                        : undefined
                    }
                  >
                    <DropdownContainer>
                      <Dropdown
                        isOpened={
                          !(
                            propertyEditInfo?.isReadonly ||
                            propertyEditInfoName?.isReadonly
                          )
                            ? handleCityDropdownToggle
                            : undefined
                        }
                        isOpen={isCityDropdownOpen}
                        data-testid="city-dropdown"
                      >
                        <Dropdown.Header data-testid="dropdown-header">
                          <HeaderWrapper
                            isOpen={isCityDropdownOpen}
                            data-testid="header-wrapper"
                          >
                            <StyledInput
                              type="text"
                              value={
                                (propertyEditInfo.hasEdit &&
                                  propertyEditInfo.oldValue) ||
                                (propertyEditInfoName.hasEdit &&
                                  propertyEditInfoName.oldValue) ||
                                citySearchText ||
                                formData.city?.name ||
                                ""
                              }
                              placeholder={translate("strCityVillage")}
                              onChange={
                                !(
                                  propertyEditInfo?.isReadonly ||
                                  propertyEditInfoName?.isReadonly
                                )
                                  ? handleCityTextChange
                                  : undefined
                              }
                              onClick={(e) => e.stopPropagation()}
                              readOnly={
                                propertyEditInfo?.isReadonly ||
                                propertyEditInfoName?.isReadonly
                              }
                            />
                          </HeaderWrapper>
                          <HeaderImage
                            isClicked={isCityDropdownOpen}
                            data-testid="header-image"
                          ></HeaderImage>
                        </Dropdown.Header>
                        {!(
                          propertyEditInfo?.isReadonly ||
                          propertyEditInfoName?.isReadonly
                        ) && (
                          <DropdownBody data-testid="dropdown-body">
                            {filteredCities.map((city) => (
                              <Wrapper
                                isOpen={isCityDropdownOpen}
                                key={city.id}
                                onClick={() => handleCitySelect(city)}
                                data-testid={`city-wrapper-${city.id}`}
                              >
                                <Translator getString={city.name} />
                              </Wrapper>
                            ))}
                          </DropdownBody>
                        )}
                      </Dropdown>
                    </DropdownContainer>
                  </PropertyEditFieldWrapper>
                );
              })()}
        </DropdownFormField>

        <DropdownFormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "PostalCode"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <DropdownContainer>
                  <Dropdown
                    isOpened={
                      !propertyEditInfo.isReadonly
                        ? handlePostalCodeDropdownToggle
                        : undefined
                    }
                    isOpen={isPostalCodeDropdownOpen}
                    data-testid="postal-code-dropdown"
                  >
                    <Dropdown.Header data-testid="postal-code-dropdown-header">
                      <HeaderWrapper
                        isOpen={isPostalCodeDropdownOpen}
                        data-testid="postal-code-header-wrapper"
                      >
                        <StyledInput
                          type="text"
                          value={
                            postalCodeSearchText || formData.postalCode || ""
                          }
                          placeholder={translate("PC")}
                          onChange={
                            !propertyEditInfo.isReadonly
                              ? handlePostalCodeTextChange
                              : undefined
                          }
                          onClick={(e) => e.stopPropagation()}
                          readOnly={propertyEditInfo.isReadonly}
                        />
                      </HeaderWrapper>
                      <HeaderImage
                        isClicked={isPostalCodeDropdownOpen}
                        data-testid="postal-code-header-image"
                      ></HeaderImage>
                    </Dropdown.Header>
                    {!propertyEditInfo.isReadonly && (
                      <DropdownBody data-testid="postal-code-dropdown-body">
                        {filteredPostCodes.map((postCode) => (
                          <Wrapper
                            isOpen={isPostalCodeDropdownOpen}
                            key={postCode}
                            onClick={() => handlePostalCodeSelect(postCode)}
                            data-testid={`postal-code-wrapper-${postCode}`}
                          >
                            {postCode}
                          </Wrapper>
                        ))}
                      </DropdownBody>
                    )}
                  </Dropdown>
                </DropdownContainer>
              </PropertyEditFieldWrapper>
            );
          })()}
        </DropdownFormField>
      </DropdownFormRow>

      <DropdownFormRow>
        <DropdownFormField>
          {formData.purpose === AddressPurpose.Abroad ? (
            <FormField>
              {(() => {
                const propertyEditInfoName = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "CityName"
                );
                const propertyEditInfo = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "CityId"
                );
                if (propertyEditInfo) {
                  propertyEditInfo.newValue =
                    cities.find((c) => c.id === propertyEditInfo.newValue)
                      ?.name ?? "";
                  propertyEditInfo.oldValue =
                    cities.find((c) => c.id === propertyEditInfo.oldValue)
                      ?.name ?? "";
                }

                return (
                  <PropertyEditFieldWrapper
                    propertyEditInfo={propertyEditInfo ?? propertyEditInfoName}
                    onApprove={
                      propertyEditInfo.pendingEdit ||
                      propertyEditInfoName.pendingEdit
                        ? async () => {
                            if (propertyEditInfoName.pendingEdit) {
                              await handleApproveProperty(
                                propertyEditInfoName.pendingEdit.id
                              );
                            }
                            if (propertyEditInfo.pendingEdit) {
                              await handleApproveProperty(
                                propertyEditInfo.pendingEdit.id
                              );
                            }
                          }
                        : undefined
                    }
                    onDecline={
                      propertyEditInfo.pendingEdit ||
                      propertyEditInfoName.pendingEdit
                        ? async () => {
                            if (propertyEditInfoName.pendingEdit) {
                              await handleDeclineProperty(
                                propertyEditInfoName.pendingEdit.id
                              );
                            }
                            if (propertyEditInfo.pendingEdit) {
                              await handleDeclineProperty(
                                propertyEditInfo.pendingEdit.id
                              );
                            }
                          }
                        : undefined
                    }
                  >
                    <Textbox
                      name="cityName"
                      label="City"
                      value={
                        propertyEditInfo.hasEdit && propertyEditInfo.oldValue
                          ? propertyEditInfo.oldValue
                          : formData.city?.name ?? formData.cityName
                      }
                      handleChange={handleChange}
                      onClick={(e) => e.stopPropagation()}
                      placeholder={translate("City")}
                      readonly={
                        propertyEditInfo?.isReadonly ||
                        propertyEditInfoName?.isReadonly
                      }
                    />
                  </PropertyEditFieldWrapper>
                );
              })()}
            </FormField>
          ) : (
            (() => {
              const propertyEditInfoName = getPropertyEditInfo(
                "Address",
                addressId || "",
                "DistrictName"
              );
              const propertyEditInfo = getPropertyEditInfo(
                "Address",
                addressId || "",
                "DistrictId"
              );
              if (propertyEditInfo) {
                propertyEditInfo.newValue =
                  districts.find((d) => d.id === propertyEditInfo.newValue)
                    ?.name ?? "";
                propertyEditInfo.oldValue =
                  districts.find((d) => d.id === propertyEditInfo.oldValue)
                    ?.name ?? "";
              }

              return (
                <PropertyEditFieldWrapper
                  propertyEditInfo={propertyEditInfo ?? propertyEditInfoName}
                  onApprove={
                    propertyEditInfo.pendingEdit ||
                    propertyEditInfoName.pendingEdit
                      ? async () => {
                          if (propertyEditInfoName.pendingEdit) {
                            await handleApproveProperty(
                              propertyEditInfoName.pendingEdit.id
                            );
                          }
                          if (propertyEditInfo.pendingEdit) {
                            await handleApproveProperty(
                              propertyEditInfo.pendingEdit.id
                            );
                          }
                        }
                      : undefined
                  }
                  onDecline={
                    propertyEditInfo.pendingEdit ||
                    propertyEditInfoName.pendingEdit
                      ? async () => {
                          if (propertyEditInfoName.pendingEdit) {
                            await handleDeclineProperty(
                              propertyEditInfoName.pendingEdit.id
                            );
                          }
                          if (propertyEditInfo.pendingEdit) {
                            await handleDeclineProperty(
                              propertyEditInfo.pendingEdit.id
                            );
                          }
                        }
                      : undefined
                  }
                >
                  <DropdownContainer>
                    <Dropdown
                      isOpened={
                        !(
                          propertyEditInfo?.isReadonly ||
                          propertyEditInfoName?.isReadonly
                        )
                          ? handleDistrictDropdownToggle
                          : undefined
                      }
                      isOpen={isDistrictDropdownOpen}
                      data-testid="district-dropdown"
                    >
                      <Dropdown.Header data-testid="district-dropdown-header">
                        <HeaderWrapper
                          isOpen={isDistrictDropdownOpen}
                          data-testid="district-header-wrapper"
                        >
                          <StyledInput
                            type="text"
                            value={
                              (propertyEditInfo.hasEdit &&
                                propertyEditInfo.oldValue) ||
                              (propertyEditInfoName.hasEdit &&
                                propertyEditInfoName.oldValue) ||
                              districtSearchText ||
                              formData.district?.name ||
                              ""
                            }
                            placeholder={translate("District")}
                            onChange={
                              !(
                                propertyEditInfo?.isReadonly ||
                                propertyEditInfoName?.isReadonly
                              )
                                ? handleDistrictTextChange
                                : undefined
                            }
                            onClick={(e) => e.stopPropagation()}
                            readOnly={
                              propertyEditInfo?.isReadonly ||
                              propertyEditInfoName?.isReadonly
                            }
                          />
                        </HeaderWrapper>
                        <HeaderImage
                          isClicked={isDistrictDropdownOpen}
                          data-testid="district-header-image"
                        ></HeaderImage>
                      </Dropdown.Header>
                      {!(
                        propertyEditInfo?.isReadonly ||
                        propertyEditInfoName?.isReadonly
                      ) && (
                        <DropdownBody data-testid="district-dropdown-body">
                          {filteredDistricts.map((district) => (
                            <Wrapper
                              isOpen={isDistrictDropdownOpen}
                              key={district.name}
                              onClick={() => handleDistrictSelect(district)}
                              data-testid={`district-wrapper-${district.name}`}
                            >
                              <Translator getString={district.name} />
                            </Wrapper>
                          ))}
                        </DropdownBody>
                      )}
                    </Dropdown>
                  </DropdownContainer>
                </PropertyEditFieldWrapper>
              );
            })()
          )}
        </DropdownFormField>
        <DropdownFormField>
          {formData.purpose === AddressPurpose.Abroad ? (
            <FormField>
              {(() => {
                const propertyEditInfo = getPropertyEditInfo(
                  "Address",
                  addressId || "",
                  "Street"
                );
                return (
                  <PropertyEditFieldWrapper
                    propertyEditInfo={propertyEditInfo}
                    onApprove={
                      propertyEditInfo.pendingEdit
                        ? () =>
                            handleApproveProperty(
                              propertyEditInfo.pendingEdit!.id
                            )
                        : undefined
                    }
                    onDecline={
                      propertyEditInfo.pendingEdit
                        ? () =>
                            handleDeclineProperty(
                              propertyEditInfo.pendingEdit!.id
                            )
                        : undefined
                    }
                  >
                    <Textbox
                      name="street"
                      label="Street"
                      value={
                        propertyEditInfo.hasEdit
                          ? propertyEditInfo.oldValue || undefined
                          : formData.street
                      }
                      handleChange={handleChange}
                      readonly={propertyEditInfo.isReadonly}
                    />
                  </PropertyEditFieldWrapper>
                );
              })()}
            </FormField>
          ) : (
            (() => {
              const propertyEditInfoName = getPropertyEditInfo(
                "Address",
                addressId || "",
                "MunicipalityName"
              );
              const propertyEditInfo = getPropertyEditInfo(
                "Address",
                addressId || "",
                "MunicipalityId"
              );
              if (propertyEditInfo) {
                propertyEditInfo.newValue =
                  municipalities.find((m) => m.id === propertyEditInfo.newValue)
                    ?.name ?? "";
                propertyEditInfo.oldValue =
                  municipalities.find((m) => m.id === propertyEditInfo.oldValue)
                    ?.name ?? "";
              }

              return (
                <PropertyEditFieldWrapper
                  propertyEditInfo={propertyEditInfo ?? propertyEditInfoName}
                  onApprove={
                    propertyEditInfo.pendingEdit ||
                    propertyEditInfoName.pendingEdit
                      ? async () => {
                          if (propertyEditInfoName.pendingEdit) {
                            await handleApproveProperty(
                              propertyEditInfoName.pendingEdit.id
                            );
                          }
                          if (propertyEditInfo.pendingEdit) {
                            await handleApproveProperty(
                              propertyEditInfo.pendingEdit.id
                            );
                          }
                        }
                      : undefined
                  }
                  onDecline={
                    propertyEditInfo.pendingEdit ||
                    propertyEditInfoName.pendingEdit
                      ? async () => {
                          if (propertyEditInfoName.pendingEdit) {
                            await handleDeclineProperty(
                              propertyEditInfoName.pendingEdit.id
                            );
                          }
                          if (propertyEditInfo.pendingEdit) {
                            await handleDeclineProperty(
                              propertyEditInfo.pendingEdit.id
                            );
                          }
                        }
                      : undefined
                  }
                >
                  <DropdownContainer>
                    <Dropdown
                      isOpened={
                        !(
                          propertyEditInfo?.isReadonly ||
                          propertyEditInfoName?.isReadonly
                        )
                          ? handleMunicipalityDropdownToggle
                          : undefined
                      }
                      isOpen={isMunicipalityDropdownOpen}
                      data-testid="municipality-dropdown"
                    >
                      <Dropdown.Header data-testid="municipality-dropdown-header">
                        <HeaderWrapper
                          isOpen={isMunicipalityDropdownOpen}
                          data-testid="municipality-header-wrapper"
                        >
                          <StyledInput
                            type="text"
                            value={
                              (propertyEditInfo.hasEdit &&
                                propertyEditInfo.oldValue) ||
                              (propertyEditInfoName.hasEdit &&
                                propertyEditInfoName.oldValue) ||
                              municipalitySearchText ||
                              formData.municipality?.name ||
                              ""
                            }
                            placeholder={translate("Municipality")}
                            onChange={
                              !(
                                propertyEditInfo?.isReadonly ||
                                propertyEditInfoName?.isReadonly
                              )
                                ? handleMunicipalityTextChange
                                : undefined
                            }
                            onClick={(e) => e.stopPropagation()}
                            readOnly={
                              propertyEditInfo?.isReadonly ||
                              propertyEditInfoName?.isReadonly
                            }
                          />
                        </HeaderWrapper>
                        <HeaderImage
                          isClicked={isMunicipalityDropdownOpen}
                          data-testid="municipality-header-image"
                        ></HeaderImage>
                      </Dropdown.Header>
                      {!(
                        propertyEditInfo?.isReadonly ||
                        propertyEditInfoName?.isReadonly
                      ) && (
                        <DropdownBody data-testid="municipality-dropdown-body">
                          {filteredMunicipalities.map((municipality) => (
                            <Wrapper
                              isOpen={isMunicipalityDropdownOpen}
                              key={municipality.name}
                              onClick={() =>
                                handleMunicipalitySelect(municipality)
                              }
                              data-testid={`municipality-wrapper-${municipality.name}`}
                            >
                              <Translator getString={municipality.name} />
                            </Wrapper>
                          ))}
                        </DropdownBody>
                      )}
                    </Dropdown>
                  </DropdownContainer>
                </PropertyEditFieldWrapper>
              );
            })()
          )}
        </DropdownFormField>
      </DropdownFormRow>

      {formData.purpose !== AddressPurpose.Abroad && (
        <FormRow>
          <FormField>
            {(() => {
              const propertyEditInfo = getPropertyEditInfo(
                "Address",
                addressId || "",
                "Neighborhood"
              );
              return (
                <PropertyEditFieldWrapper
                  propertyEditInfo={propertyEditInfo}
                  onApprove={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleApproveProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                  onDecline={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleDeclineProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                >
                  <Textbox
                    name="neighborhood"
                    label="Region"
                    value={
                      propertyEditInfo.hasEdit
                        ? propertyEditInfo.oldValue || undefined
                        : formData.neighborhood
                    }
                    handleChange={handleChange}
                    readonly={propertyEditInfo.isReadonly}
                  />
                </PropertyEditFieldWrapper>
              );
            })()}
          </FormField>
          <FormField>
            {(() => {
              const propertyEditInfo = getPropertyEditInfo(
                "Address",
                addressId || "",
                "Street"
              );
              return (
                <PropertyEditFieldWrapper
                  propertyEditInfo={propertyEditInfo}
                  onApprove={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleApproveProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                  onDecline={
                    propertyEditInfo.pendingEdit
                      ? () =>
                          handleDeclineProperty(
                            propertyEditInfo.pendingEdit!.id
                          )
                      : undefined
                  }
                >
                  <Textbox
                    name="street"
                    label="Street"
                    value={
                      propertyEditInfo.hasEdit
                        ? propertyEditInfo.oldValue || undefined
                        : formData.street
                    }
                    handleChange={handleChange}
                    readonly={propertyEditInfo.isReadonly}
                  />
                </PropertyEditFieldWrapper>
              );
            })()}
          </FormField>
        </FormRow>
      )}

      <FormRow>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "Block"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="block"
                  label="Block"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.block
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "Apartment"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="apartment"
                  label="Apartment"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.apartment
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
      </FormRow>

      <SectionHeader>
        <Image src={phoneDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("Phone number")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <FormRow>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "Phone"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="phone"
                  label="Phone number"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.phone
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "WorkPhone"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="workPhone"
                  label="Work phone"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.workPhone
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
      </FormRow>

      <SectionHeader>
        <Image src={mailDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("E-mail")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <SingleFieldRow>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "Email"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="email"
                  label="Е-mail"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.email
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
      </SingleFieldRow>

      <SingleFieldRow>
        <FormField>
          {(() => {
            const propertyEditInfo = getPropertyEditInfo(
              "Address",
              addressId || "",
              "WorkEmail"
            );
            return (
              <PropertyEditFieldWrapper
                propertyEditInfo={propertyEditInfo}
                onApprove={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleApproveProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
                onDecline={
                  propertyEditInfo.pendingEdit
                    ? () =>
                        handleDeclineProperty(propertyEditInfo.pendingEdit!.id)
                    : undefined
                }
              >
                <Textbox
                  name="workEmail"
                  label="Work E-mail"
                  value={
                    propertyEditInfo.hasEdit
                      ? propertyEditInfo.oldValue || undefined
                      : formData.workEmail
                  }
                  handleChange={handleChange}
                  readonly={propertyEditInfo.isReadonly}
                />
              </PropertyEditFieldWrapper>
            );
          })()}
        </FormField>
      </SingleFieldRow>

      {!isNewEmployeeFlow && (
        <SaveButtonContainer>
          <SaveButton
            label={translate("Add")}
            onClick={handleSave}
            data-testid="save-address-button"
          />
        </SaveButtonContainer>
      )}
    </FormContainer>
  );
};

export default NewAddress;
