using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.FileReaders;
using WorkTimeApi.Database.GuidGenerator;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.Notifications;

namespace WorkTimeApi.Database
{
    public static class ModelBuilderExtensions
    {
        public static void Seed(this ModelBuilder modelBuilder, IConfiguration configuration)
        {
            SeedMod(modelBuilder);
            SeedQualificationGroups(modelBuilder);
            SeedTZPBs(modelBuilder);
            SeedDefaultLocationData(modelBuilder);
            SeedNKPDData(modelBuilder);
            SeedKidData(modelBuilder);
            SeedRolesAndPermissions(modelBuilder, configuration);
            SeedNotificationTypes(modelBuilder);
        }

        public static void SeedMod(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Mod>().HasData(
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD1"), Code = 1, Sector = "A", KidCode = "01", KidDescription = "Растениевъдство, животновъдство и лов, спомагателни дейности", Group1 = 1034, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD2"), Code = 1, Sector = "A", KidCode = "03", KidDescription = "Рибно стопанство", Group1 = 1034, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD3"), Code = 2, Sector = "A", KidCode = "02", KidDescription = "Горско стопанство", Group1 = 935, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD4"), Code = 3, Sector = "A", KidCode = "0149", KidDescription = "Отглеждане на други животни (пчеларство)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD5"), Code = 4, Sector = "B", KidCode = "05", KidDescription = "Добив на въглища", Group1 = 901, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD6"), Code = 5, Sector = "B", KidCode = "06", KidDescription = "Добив на нефт и природен газ", Group1 = 865, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD7"), Code = 6, Sector = "B", KidCode = "07", KidDescription = "Добив на метални руди", Group1 = 926, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD8"), Code = 7, Sector = "B", KidCode = "0811", KidDescription = "Добив на строителни материали", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD9"), Code = 8, Sector = "B", KidCode = "0811", KidDescription = "Добив на декоративни скални материали", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD10"), Code = 9, Sector = "B", KidCode = "0811", KidDescription = "Добив на варовик, суров гипс, креда, доломит и шисти", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD11"), Code = 10, Sector = "B", KidCode = "0812", KidDescription = "Добив на трошен камък, чакъл и пясък", Group1 = 890, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD12"), Code = 11, Sector = "B", KidCode = "0812", KidDescription = "Добив на глина и каолин", Group1 = 916, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD13"), Code = 12, Sector = "B", KidCode = "089", KidDescription = "Добив на други неметални материали и суровини", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD14"), Code = 12, Sector = "B", KidCode = "09", KidDescription = "Спомагателни дейности в добива", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD15"), Code = 13, Sector = "C", KidCode = "101", KidDescription = "Производство и преработка на месо; производство на месни продукти, без готови ястия", Group1 = 1486, Group2 = 982, Group3 = 784, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD16"), Code = 13, Sector = "C", KidCode = "102", KidDescription = "Преработка и консервиране на риба и други водни животни, без готови ястия", Group1 = 1486, Group2 = 982, Group3 = 784, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD17"), Code = 14, Sector = "C", KidCode = "1012", KidDescription = "Производство и преработка на месо от домашни птици", Group1 = 1226, Group2 = 1018, Group3 = 883, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD18"), Code = 15, Sector = "C", KidCode = "103", KidDescription = "Преработка и консервиране на плодове и зеленчуци, без готови ястия", Group1 = 904, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD19"), Code = 16, Sector = "C", KidCode = "104", KidDescription = "Производство на растителни и животински масла и мазнини", Group1 = 982, Group2 = 883, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD20"), Code = 17, Sector = "C", KidCode = "105", KidDescription = "Производство на мляко и млечни продукти", Group1 = 1057, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD21"), Code = 18, Sector = "C", KidCode = "106", KidDescription = "Производство на мелничарски продукти, нишесте и нишестени продукти", Group1 = 1247, Group2 = 1039, Group3 = 987, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD22"), Code = 19, Sector = "C", KidCode = "107", KidDescription = "Производство на хлебни и тестени изделия", Group1 = 831, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD23"), Code = 19, Sector = "C", KidCode = "108", KidDescription = "Производство на други хранителни продукти", Group1 = 831, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD24"), Code = 20, Sector = "C", KidCode = "1081", KidDescription = "Производство на захар", Group1 = 1143, Group2 = 940, Group3 = 873, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD25"), Code = 20, Sector = "C", KidCode = "1082", KidDescription = "Производство на какао, шоколадови и захарни изделия", Group1 = 1143, Group2 = 940, Group3 = 873, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD26"), Code = 21, Sector = "C", KidCode = "109", KidDescription = "Производство на готови храни за животни", Group1 = 1122, Group2 = 987, Group3 = 894, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD27"), Code = 22, Sector = "C", KidCode = "11", KidDescription = "Производство на напитки", Group1 = 869, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD28"), Code = 23, Sector = "C", KidCode = "1105", KidDescription = "Производство на пиво и малц", Group1 = 821, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD29"), Code = 23, Sector = "C", KidCode = "1106", KidDescription = "Производство на пиво и малц", Group1 = 821, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD30"), Code = 24, Sector = "C", KidCode = "1107", KidDescription = "Производство на безалкохолни напитки, минерални и други бутилирани води", Group1 = 935, Group2 = 790, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD31"), Code = 25, Sector = "C", KidCode = "12", KidDescription = "Производство на тютюневи изделия", Group1 = 1034, Group2 = 1008, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD32"), Code = 26, Sector = "C", KidCode = "13", KidDescription = "Производство на текстил и изделия от текстил, без облекло", Group1 = 987, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD33"), Code = 27, Sector = "C", KidCode = "14", KidDescription = "Производство на облекло", Group1 = 925, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD34"), Code = 28, Sector = "C", KidCode = "143", KidDescription = "Производство на други трикотажни изделия", Group1 = 997, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD35"), Code = 29, Sector = "C", KidCode = "15", KidDescription = "Обработка на кожи; производство на обувки и други изделия от обработени кожи без косъм", Group1 = 906, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD36"), Code = 30, Sector = "C", KidCode = "16", KidDescription = "Производство на дървен материал и изделия от дървен материал и корк, без мебели; производство на изделия от слама и материали за плетене", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD37"), Code = 31, Sector = "C", KidCode = "17", KidDescription = "Производство на хартия, картон и изделия от хартия и картон", Group1 = 852, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD38"), Code = 32, Sector = "C", KidCode = "18", KidDescription = "Печатна дейност и възпроизвеждане на записани носители", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD39"), Code = 32, Sector = "J", KidCode = "58", KidDescription = "Издателска дейност", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD40"), Code = 32, Sector = "J", KidCode = "59", KidDescription = "Производство на филми и телевизионни предавания, звукозаписване и издаване на музика", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD41"), Code = 33, Sector = "C", KidCode = "19", KidDescription = "Производство на кокс и рафинирани нефтопродукти", Group1 = 1763, Group2 = 1310, Group3 = 1034, Group4 = 780, Group5 = 882, Group6 = 780, Group7 = 958, Group8 = 967, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD42"), Code = 34, Sector = "C", KidCode = "20", KidDescription = "Производство на химични продукти", Group1 = 870, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD43"), Code = 35, Sector = "C", KidCode = "21", KidDescription = "Производство на лекарствени вещества и продукти", Group1 = 870, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD44"), Code = 36, Sector = "C", KidCode = "22", KidDescription = "Производство на изделия от каучук и пластмаси", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD45"), Code = 37, Sector = "C", KidCode = "23", KidDescription = "Производство на изделия от други неметални минерални суровини", Group1 = 827, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD46"), Code = 38, Sector = "C", KidCode = "24", KidDescription = "Производство на основни метали", Group1 = 810, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD47"), Code = 39, Sector = "C", KidCode = "245", KidDescription = "Леене на метали", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD48"), Code = 40, Sector = "C", KidCode = "25", KidDescription = "Производство на метални изделия, без машини и оборудване", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD49"), Code = 41, Sector = "C", KidCode = "26", KidDescription = "Производство на компютърна и комуникационна техника, електронни и оптични продукти", Group1 = 790, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD50"), Code = 42, Sector = "C", KidCode = "27", KidDescription = "Производство на електрически съоръжения", Group1 = 790, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD51"), Code = 43, Sector = "C", KidCode = "28", KidDescription = "Производство на машини и оборудване с общо и специално предназначение", Group1 = 862, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD52"), Code = 43, Sector = "C", KidCode = "254", KidDescription = "Производство на въоръжение и боеприпаси", Group1 = 790, Group2 = 780, Group3 = 862, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD53"), Code = 44, Sector = "C", KidCode = "2811", KidDescription = "Производство на турбини и двигатели, без авиационни, автомобилни и мотоциклетни", Group1 = 987, Group2 = 780, Group3 = 862, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD54"), Code = 45, Sector = "C", KidCode = "29", KidDescription = "Производство на автомобили, ремаркета и полуремаркета", Group1 = 1018, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD55"), Code = 45, Sector = "C", KidCode = "30", KidDescription = "Производство на превозни средства, без автомобили", Group1 = 1018, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD56"), Code = 46, Sector = "C", KidCode = "31", KidDescription = "Производство на мебели", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD57"), Code = 47, Sector = "C", KidCode = "32", KidDescription = "Производство, некласифицирано другаде", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD58"), Code = 47, Sector = "C", KidCode = "33", KidDescription = "Ремонт и инсталиране на машини и оборудване", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD59"), Code = 48, Sector = "C", KidCode = "325", KidDescription = "Производство на медицински и зъболекарски инструменти и средства (Дейности в зъботехнически лаборатории)", Group1 = 929, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD60"), Code = 49, Sector = "D", KidCode = "351", KidDescription = "Производство, пренос и разпределение на електрическа енергия", Group1 = 987, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD61"), Code = 50, Sector = "D", KidCode = "352", KidDescription = "Производство и разпределение на газообразни горива по газоразпределителните мрежи", Group1 = 1461, Group2 = 1008, Group3 = 958, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD62"), Code = 50, Sector = "D", KidCode = "495", KidDescription = "Тръбопроводен транспорт", Group1 = 1461, Group2 = 1008, Group3 = 958, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD63"), Code = 51, Sector = "D", KidCode = "353", KidDescription = "Производство и разпределение на топлинна енергия", Group1 = 873, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD64"), Code = 52, Sector = "E", KidCode = "36", KidDescription = "Събиране, пречистване и доставяне на води", Group1 = 810, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD65"), Code = 52, Sector = "E", KidCode = "37", KidDescription = "Събиране, отвеждане и пречистване на отпадъчни води", Group1 = 810, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD66"), Code = 53, Sector = "E", KidCode = "38", KidDescription = "Събиране и обезвреждане на отпадъци; рециклиране на материали", Group1 = 832, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD67"), Code = 53, Sector = "E", KidCode = "39", KidDescription = " Възстановяване и други услуги по управление на отпадъци", Group1 = 832, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD68"), Code = 54, Sector = "E", KidCode = "3812", KidDescription = "Събиране на опасни отпадъци", Group1 = 1013, Group2 = 842, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD69"), Code = 54, Sector = "E", KidCode = "3822", KidDescription = "Обработване и обезвреждане на опасни отпадъци", Group1 = 1013, Group2 = 842, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD70"), Code = 55, Sector = "F", KidCode = "41", KidDescription = "Строителство на сгради", Group1 = 790, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD71"), Code = 55, Sector = "F", KidCode = "42", KidDescription = "Строителство на съоръжения", Group1 = 790, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD72"), Code = 55, Sector = "F", KidCode = "43", KidDescription = "Специализирани строителни дейности", Group1 = 790, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD73"), Code = 56, Sector = "F", KidCode = "4211", KidDescription = "Строителство на автомагистрали, пътища и самолетни писти", Group1 = 878, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD74"), Code = 57, Sector = "F", KidCode = "4222", KidDescription = "Строителство на преносни и разпределителни електрически и далекосъобщителни мрежи", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD75"), Code = 58, Sector = "G", KidCode = "45", KidDescription = "Търговия на едро и дребно с автомобили и мотоциклети, техническо обслужване и ремонт", Group1 = 1299, Group2 = 1070, Group3 = 945, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD76"), Code = 58, Sector = "G", KidCode = "46", KidDescription = "Търговия на едро, без търговията с автомобили и мотоциклети", Group1 = 1299, Group2 = 1070, Group3 = 945, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD77"), Code = 58, Sector = "G", KidCode = "47", KidDescription = " Търговия на дребно, без търговията с автомобили и мотоциклети", Group1 = 1299, Group2 = 1070, Group3 = 945, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD78"), Code = 59, Sector = "G", KidCode = "4646", KidDescription = "Търговия на едро с фармацевтични стоки, медицинска техника и апаратура", Group1 = 923, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD79"), Code = 59, Sector = "G", KidCode = "4773", KidDescription = "Търговия на дребно с лекарства и други фармацевтични стоки", Group1 = 923, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD80"), Code = 59, Sector = "G", KidCode = "4774", KidDescription = "Търговия на дребно с медицински и ортопедични стоки", Group1 = 923, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD81"), Code = 60, Sector = "I", KidCode = "79", KidDescription = "Туристическа агентска и операторска дейност; други дейности, свързани с пътувания и резервации", Group1 = 966, Group2 = 831, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD82"), Code = 61, Sector = "I", KidCode = "55", KidDescription = "Хотелиерство", Group1 = 1266, Group2 = 1090, Group3 = 800, Group4 = 800, Group5 = 800, Group6 = 800, Group7 = 800, Group8 = 800, Group9 = 800, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD83"), Code = 61, Sector = "I", KidCode = "56", KidDescription = "Ресторантьорство", Group1 = 1266, Group2 = 1090, Group3 = 800, Group4 = 800, Group5 = 800, Group6 = 800, Group7 = 800, Group8 = 800, Group9 = 800, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD84"), Code = 62, Sector = "H", KidCode = "491", KidDescription = "Пътнически железопътен транспорт, междуселищен", Group1 = 927, Group2 = 822, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD85"), Code = 62, Sector = "H", KidCode = "492", KidDescription = "Товарен железопътен транспорт", Group1 = 927, Group2 = 822, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD86"), Code = 62, Sector = "H", KidCode = "52", KidDescription = "Складиране на товари и спомагателни дейности в железопътния транспорт", Group1 = 927, Group2 = 822, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD87"), Code = 63, Sector = "H", KidCode = "49.3", KidDescription = "Друг пътнически сухопътен транспорт", Group1 = 845, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD88"), Code = 63, Sector = "H", KidCode = "49.4", KidDescription = "Товарен автомобилен транспорт и услуги по преместване", Group1 = 845, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD89"), Code = 64, Sector = "H", KidCode = "50", KidDescription = "Воден транспорт", Group1 = 1201, Group2 = 924, Group3 = 789, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD90"), Code = 65, Sector = "H", KidCode = "51", KidDescription = "Въздушен транспорт", Group1 = 1011, Group2 = 1103, Group3 = 900, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD91"), Code = 66, Sector = "H", KidCode = "52", KidDescription = "Складиране на товари и спомагателни дейности в транспорта (без железопътен транспорт)", Group1 = 1038, Group2 = 973, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD92"), Code = 67, Sector = "H", KidCode = "53", KidDescription = "Пощенски и куриерски дейности", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD93"), Code = 68, Sector = "J", KidCode = "60", KidDescription = "Радио- и телевизионна дейност", Group1 = 933, Group2 = 817, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD94"), Code = 68, Sector = "J", KidCode = "61", KidDescription = "Далекосъобщения", Group1 = 933, Group2 = 817, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD95"), Code = 69, Sector = "K", KidCode = "64", KidDescription = "ФИНАНСОВИ И ЗАСТРАХОВАТЕЛНИ ДЕЙНОСТИ", Group1 = 1589, Group2 = 1008, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD96"), Code = 69, Sector = "K", KidCode = "65", KidDescription = "ФИНАНСОВИ И ЗАСТРАХОВАТЕЛНИ ДЕЙНОСТИ", Group1 = 1589, Group2 = 1008, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD97"), Code = 69, Sector = "K", KidCode = "66", KidDescription = "ФИНАНСОВИ И ЗАСТРАХОВАТЕЛНИ ДЕЙНОСТИ", Group1 = 1589, Group2 = 1008, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD98"), Code = 70, Sector = "J", KidCode = "62", KidDescription = "Дейности в областта на информационните технологии", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD99"), Code = 70, Sector = "J", KidCode = "63", KidDescription = "Информационни услуги", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD100"), Code = 70, Sector = "L", KidCode = "68", KidDescription = "ОПЕРАЦИИ С НЕДВИЖИМИ ИМОТИ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD101"), Code = 70, Sector = "M", KidCode = "69", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD102"), Code = 70, Sector = "N", KidCode = "70", KidDescription = "АДМИНИСТРАТИВНИ И СПОМАГАТЕЛНИ ДЕЙНОСТИ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD103"), Code = 70, Sector = "M", KidCode = "71", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD104"), Code = 70, Sector = "M", KidCode = "73", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD105"), Code = 70, Sector = "M", KidCode = "74", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD106"), Code = 70, Sector = "M", KidCode = "77", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD107"), Code = 70, Sector = "M", KidCode = "78", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD108"), Code = 70, Sector = "M", KidCode = "81", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD109"), Code = 70, Sector = "M", KidCode = "82", KidDescription = "ПРОФЕСИОНАЛНИ ДЕЙНОСТИ И НАУЧНИ ИЗСЛЕДВАНИЯ", Group1 = 1008, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD110"), Code = 71, Sector = "N", KidCode = "80", KidDescription = "Дейности по охрана и разследване", Group1 = 848, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD111"), Code = 72, Sector = "M", KidCode = "72", KidDescription = "Научноизследователска и развойна дейност", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD112"), Code = 73, Sector = "O", KidCode = "84", KidDescription = "ДЪРЖАВНО УПРАВЛЕНИЕ", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD113"), Code = 74, Sector = "P", KidCode = "85", KidDescription = "ОБРАЗОВАНИЕ", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD114"), Code = 75, Sector = "Q", KidCode = "86", KidDescription = "ХУМАННО ЗДРАВЕОПАЗВАНЕ И СОЦИАЛНА РАБОТА (без медицинска сестра, акушерка, рехабилитатор, фелдшер и лаборант, включително главните и старшите)", Group1 = 1045, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD115"), Code = 75, Sector = "Q", KidCode = "75", KidDescription = "Ветеринарномедицинска дейност", Group1 = 1045, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD116"), Code = 76, Sector = "Q", KidCode = "86", KidDescription = "Дейност на болници (без началник-клиника/отделение, медицинска сестра, акушерка, рехабилитатор, фелдшер и лаборант, включително главните и старшите)", Group1 = 1336, Group2 = 987, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD117"), Code = 77, Sector = "Q", KidCode = "87", KidDescription = "Медико-социални грижи с настаняване", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD118"), Code = 77, Sector = "Q", KidCode = "88", KidDescription = "Социална работа без настаняване", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD119"), Code = 78, Sector = "R", KidCode = "90", KidDescription = "КУЛТУРА, СПОРТ И РАЗВЛЕЧЕНИЯ (за 93.12 Дейност на спортни клубове – без професионален спортист във футболен клуб)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD120"), Code = 78, Sector = "R", KidCode = "91", KidDescription = "КУЛТУРА, СПОРТ И РАЗВЛЕЧЕНИЯ (за 93.12 Дейност на спортни клубове – без професионален спортист във футболен клуб)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD121"), Code = 78, Sector = "R", KidCode = "92", KidDescription = "КУЛТУРА, СПОРТ И РАЗВЛЕЧЕНИЯ (за 93.12 Дейност на спортни клубове – без професионален спортист във футболен клуб)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD122"), Code = 78, Sector = "R", KidCode = "93", KidDescription = "КУЛТУРА, СПОРТ И РАЗВЛЕЧЕНИЯ (за 93.12 Дейност на спортни клубове – без професионален спортист във футболен клуб)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD123"), Code = 79, Sector = "S", KidCode = "95", KidDescription = "Ремонт на компютърна техника, на лични и домакински вещи", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD124"), Code = 79, Sector = "S", KidCode = "96", KidDescription = "Други персонални услуги", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD125"), Code = 79, Sector = "T", KidCode = "97", KidDescription = "ДЕЙНОСТИ НА ДОМАКИНСТВА КАТО РАБОТОДАТЕЛИ", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD126"), Code = 80, Sector = "S", KidCode = "94", KidDescription = "Дейности на организации с нестопанска цел (без дейност на религиозни организации)", Group1 = 896, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD127"), Code = 80, Sector = "U", KidCode = "99", KidDescription = "Дейности на екстериториални организации и служби", Group1 = 896, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD128"), Code = 81, Sector = "R", KidCode = "9312", KidDescription = "Дейност на спортни клубове (само за професионален спортист във футболен клуб)", Group1 = 0, Group2 = 0, Group3 = 780, Group4 = 0, Group5 = 0, Group6 = 0, Group7 = 0, Group8 = 0, Group9 = 0, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD129"), Code = 82, Sector = "", KidCode = "", KidDescription = "Централен кооперативен съюз", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 780, Group5 = 780, Group6 = 780, Group7 = 780, Group8 = 780, Group9 = 780, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD130"), Code = 83, Sector = "Q", KidCode = "86", KidDescription = "ХУМАННО ЗДРАВЕОПАЗВАНЕ И СОЦИАЛНА РАБОТА (само за медицинска сестра, акушерка, рехабилитатор, фелдшер и лаборант)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 0, Group5 = 0, Group6 = 0, Group7 = 0, Group8 = 0, Group9 = 0, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD131"), Code = 84, Sector = "Q", KidCode = "861", KidDescription = "Дейност на болници (само за началник-клиника/отделение)", Group1 = 1103, Group2 = 0, Group3 = 0, Group4 = 0, Group5 = 0, Group6 = 0, Group7 = 0, Group8 = 0, Group9 = 0, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD132"), Code = 85, Sector = "Q", KidCode = "861", KidDescription = "Дейност на болници (само за медицинска сестра, акушерка, рехабилитатор, фелдшер и лаборант)", Group1 = 780, Group2 = 780, Group3 = 780, Group4 = 0, Group5 = 0, Group6 = 0, Group7 = 0, Group8 = 0, Group9 = 0, ValidFrom = new DateTime(2023, 01, 01) },
              new Mod { Id = GuidGeneratorUtility.GenerateGuid("MOD133"), Code = 86, Sector = "S", KidCode = "9491", KidDescription = "Дейност на религиозни организации", Group1 = 0, Group2 = 780, Group3 = 0, Group4 = 0, Group5 = 0, Group6 = 0, Group7 = 0, Group8 = 0, Group9 = 0, ValidFrom = new DateTime(2023, 01, 01) }
              );
        }

        public static void SeedQualificationGroups(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<QualificationGroup>().HasData(
             new QualificationGroup { Id = 1, Name = "Ръководни служители", Description = "Президент, законодатели, висши служители и ръководители" },
             new QualificationGroup { Id = 2, Name = "Аналитични специалисти", Description = "Аналитични специалисти" },
             new QualificationGroup { Id = 3, Name = "Приложни специалисти", Description = "Техници и други приложни специалисти" },
             new QualificationGroup { Id = 4, Name = "Помощен персонал", Description = "Административен персонал" },
             new QualificationGroup { Id = 5, Name = "Персонал зает с други услуги", Description = "Персонал, зает с услуги на населението, търговията и охраната" },
             new QualificationGroup { Id = 6, Name = "Производители в сел. стопанство", Description = "Квалифицирани работници в селското, горското, рибното и ловното" },
             new QualificationGroup { Id = 7, Name = "Квалифицирани п-ни работници", Description = "Квалифицирани производствени работници и сродни на тях занаятчии" },
             new QualificationGroup { Id = 8, Name = "Оператори на машини", Description = "Оператори на машини и съръжения, и работници по монтаж на изделия" },
             new QualificationGroup { Id = 9, Name = "Нискоквалифицирани работници", Description = "Професии, неизискващи специална квалификация" }
         );
        }

        public static void SeedTZPBs(ModelBuilder modelBuilder)
        {

            modelBuilder.Entity<TZPB>().HasData(
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB1"), KIDCode = "01", KIDDescription = "Растениевъдство, животновъдство и лов; спомагателни дейности", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB2"), KIDCode = "02", KIDDescription = "Горско стопанство", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB3"), KIDCode = "03", KIDDescription = "Рибно стопанство", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB4"), KIDCode = "05", KIDDescription = "Добив на въглища", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB5"), KIDCode = "06", KIDDescription = "Добив на нефт и природен газ", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB6"), KIDCode = "07", KIDDescription = "Добив на метални руди", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB7"), KIDCode = "08", KIDDescription = "Добив на неметални материали и суровини", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB8"), KIDCode = "09", KIDDescription = "Спомагателни дейности в добива", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB9"), KIDCode = "10", KIDDescription = "Производство на хранителни продукти", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB11"), KIDCode = "11", KIDDescription = "Производство на напитки", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB12"), KIDCode = "12", KIDDescription = "Производство на тютюневи изделия", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB13"), KIDCode = "13", KIDDescription = "Производство на текстил и изделия от текстил, без облекло", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB14"), KIDCode = "14", KIDDescription = "Производство на облекло", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB15"), KIDCode = "15", KIDDescription = "Обработка на кожи; производство на обувки и други изделия от обработени кожи без косъм", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB16"), KIDCode = "16", KIDDescription = "Производство на дървен материал и изделия от дървен материал и корк, без мебели; производство на изделия от слама и материали за плетене", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB17"), KIDCode = "17", KIDDescription = "Производство на хартия, картон и изделия от хартия и картон", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB18"), KIDCode = "18", KIDDescription = "Печатна дейност и възпроизвеждане на записани носители", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB19"), KIDCode = "19", KIDDescription = "Производство на кокс и рафинирани нефтопродукти", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB20"), KIDCode = "20", KIDDescription = "Производство на химични продукти", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB21"), KIDCode = "21", KIDDescription = "Производство на лекарствени вещества и продукти", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB22"), KIDCode = "22", KIDDescription = "Производство на изделия от каучук и пластмаси", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB23"), KIDCode = "23", KIDDescription = "Производство на изделия от други неметални минерални суровини", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB24"), KIDCode = "24", KIDDescription = "Производство на основни метали", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB25"), KIDCode = "25", KIDDescription = "Производство на метални изделия, без машини и оборудване", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB26"), KIDCode = "26", KIDDescription = "Производство на компютърна и комуникационна техника, електронни и оптични продукти", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB27"), KIDCode = "27", KIDDescription = "Производство на електрически съоръжения", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB28"), KIDCode = "28", KIDDescription = "Производство на машини и оборудване, с общо и специално предназначение", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB29"), KIDCode = "29", KIDDescription = "Производство на автомобили, ремаркета и полуремаркета", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB30"), KIDCode = "30", KIDDescription = "Производство на превозни средства, без автомобили", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB31"), KIDCode = "31", KIDDescription = "Производство на мебели", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB32"), KIDCode = "32", KIDDescription = "Производство, некласифицирано другаде", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB33"), KIDCode = "33", KIDDescription = "Ремонт и инсталиране на машини и оборудване", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB35"), KIDCode = "35", KIDDescription = "Производство и разпределение на електрическа и топлинна енергия и на газообразни горива", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB36"), KIDCode = "36", KIDDescription = "Събиране, пречистване и доставяне на води", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB37"), KIDCode = "37", KIDDescription = "Събиране, отвеждане и пречистване на отпадъчни води", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB38"), KIDCode = "38", KIDDescription = "Събиране и обезвреждане на отпадъци; рециклиране на материали", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB39"), KIDCode = "39", KIDDescription = "Възстановяване и други услуги по управление на отпадъци", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB41"), KIDCode = "41", KIDDescription = "Строителство на сгради", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB42"), KIDCode = "42", KIDDescription = "Строителство на съоръжения", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB43"), KIDCode = "43", KIDDescription = "Специализирани строителни дейности", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB45"), KIDCode = "45", KIDDescription = "Търговия на едро и дребно с автомобили и мотоциклети, техническо обслужване и ремонт", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB46"), KIDCode = "46", KIDDescription = "Търговия на едро, без търговията с автомобили и мотоциклети", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB47"), KIDCode = "47", KIDDescription = "Търговия на дребно, без търговията с автомобили и мотоциклети", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB49"), KIDCode = "49", KIDDescription = "Сухопътен транспорт", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB50"), KIDCode = "50", KIDDescription = "Воден транспорт", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB51"), KIDCode = "51", KIDDescription = "Въздушен транспорт", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB52"), KIDCode = "52", KIDDescription = "Складиране на товари и спомагателни дейности в транспорта", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB53"), KIDCode = "53", KIDDescription = "Пощенски и куриерски дейности", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB55"), KIDCode = "55", KIDDescription = "Хотелиерство", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB56"), KIDCode = "56", KIDDescription = "Ресторантьорство", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB58"), KIDCode = "58", KIDDescription = "Издателска дейност", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB59"), KIDCode = "59", KIDDescription = "Производство на филми и телевизионни предавания, звукозаписване и издаване на музика", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB60"), KIDCode = "60", KIDDescription = "Радио- и телевизионна дейност", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB61"), KIDCode = "61", KIDDescription = "Далекосъобщения", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB62"), KIDCode = "62", KIDDescription = "Дейности в областта на информационните технологии", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB63"), KIDCode = "63", KIDDescription = "Информационни услуги", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB64"), KIDCode = "64", KIDDescription = "Предоставяне на финансови услуги, без застраховане и допълнително пенсионно осигуряване", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB65"), KIDCode = "65", KIDDescription = "Застраховане, презастраховане и допълнително пенсионно осигуряване", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB66"), KIDCode = "66", KIDDescription = "Спомагателни дейности във финансовите услуги и застраховането", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB68"), KIDCode = "68", KIDDescription = "Операции с недвижими имоти", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB69"), KIDCode = "69", KIDDescription = "Юридически и счетоводни дейности", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB70"), KIDCode = "70", KIDDescription = "Дейност на централни офиси; консултантски дейности в областта на управлението", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB71"), KIDCode = "71", KIDDescription = "Архитектурни и инженерни дейности; технически изпитвания и анализи", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB72"), KIDCode = "72", KIDDescription = "Научноизследователска и развойна дейност", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB73"), KIDCode = "73", KIDDescription = "Рекламна дейност и проучване на пазари", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB74"), KIDCode = "74", KIDDescription = "Други професионални дейности", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB75"), KIDCode = "75", KIDDescription = "Ветеринарномедицинска дейност", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB77"), KIDCode = "77", KIDDescription = "Даване под наем и оперативен лизинг", Value = 1.1M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB78"), KIDCode = "78", KIDDescription = "Дейности по наемане и предоставяне на работна сила", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB79"), KIDCode = "79", KIDDescription = "Туристическа агентска и операторска дейност; други дейности, свързани с пътувания и резервации", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB80"), KIDCode = "80", KIDDescription = "Дейности по охрана и разследване", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB81"), KIDCode = "81", KIDDescription = "Дейности по обслужване на сгради и озеленяване", Value = 0.9M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB82"), KIDCode = "82", KIDDescription = "Административни офис дейности и друго спомагателно обслужване на стопанската дейност", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB84"), KIDCode = "84", KIDDescription = "Държавно управление", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB85"), KIDCode = "85", KIDDescription = "Образование", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB86"), KIDCode = "86", KIDDescription = "Хуманно здравеопазване", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB87"), KIDCode = "87", KIDDescription = "Медико-социални грижи с настаняване", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB88"), KIDCode = "88", KIDDescription = "Социална работа без настаняване", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB90"), KIDCode = "90", KIDDescription = "Артистична и творческа дейност", Value = 0.7M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB91"), KIDCode = "91", KIDDescription = "Други дейности в областта на културата", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB92"), KIDCode = "92", KIDDescription = "Организиране на хазартни игри", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB93"), KIDCode = "93", KIDDescription = "Спортни и други дейности, свързани с развлечения и отдих", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB94"), KIDCode = "94", KIDDescription = "Дейности на организации с нестопанска цел", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB95"), KIDCode = "95", KIDDescription = "Ремонт на компютърна техника, на лични и домакински вещи", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB96"), KIDCode = "96", KIDDescription = "Други персонални услуги", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB97"), KIDCode = "97", KIDDescription = "Дейности на домакинства като работодатели на домашен персонал", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB98"), KIDCode = "98", KIDDescription = "Недиференцирани дейности на домакинства по производство на стоки и услуги за собствено потребление", Value = 0.4M, ValidFrom = new DateTime(2023, 08, 01) },
                new TZPB { Id = GuidGeneratorUtility.GenerateGuid("TZPB99"), KIDCode = "99", KIDDescription = "Дейности на екстериториални организации и служби", Value = 0.5M, ValidFrom = new DateTime(2023, 08, 01) }
            );
        }

        public static void SeedDefaultLocationData(ModelBuilder modelBuilder)
        {
            DefaultLocationExcelReader defaultLocationCityReader = new DefaultLocationExcelReader();
            var cities = new List<City>();
            var districts = new List<District>();
            var municipalities = new List<Municipality>();

            (districts, municipalities, cities) = defaultLocationCityReader.ReadExcelFile();

            modelBuilder.Entity<District>().HasData(
              districts
               );
            modelBuilder.Entity<Municipality>().HasData(
              municipalities
               );
            modelBuilder.Entity<City>().HasData(
              cities
               );
        }

        public static void SeedNKPDData(ModelBuilder modelBuilder)
        {
            var reader = new NKPDExcelReader();
            var nkpdList = reader.ReadExcelFile(2023);
            nkpdList.AddRange(reader.ReadExcelFile(2025));

            modelBuilder.Entity<KPD>().HasData(nkpdList);
        }

        public static void SeedKidData(ModelBuilder modelBuilder)
        {
            var reader = new KidExcelReader();
            var kidList = reader.ReadExcelFile(2008);
            kidList.AddRange(reader.ReadExcelFile(2025));

            modelBuilder.Entity<Kid>().HasData(kidList);
        }

        public static void SeedRolesAndPermissions(ModelBuilder modelBuilder, IConfiguration configuration)
        {
            var defaultCompanyId = Guid.Parse(configuration["WorkTimeDefaultCompanyId"]
                ?? throw new Exception("Не е добавена дефоултна стойност за WorkTimeCompanyId!"));

            var permissions = GetNames(typeof(DefaultPermissions));

            var data = permissions
                .Select(p => new Permission()
                {
                    Id = DefaultPermissions.CreateGuidFromName(p),
                    Name = p
                })
                .ToList();

            modelBuilder
                .Entity<Permission>()
                .HasData(data);

            var owner = new Role()
            {
                Id = Guid.Parse("c5835183-271f-400d-a1de-34753816559c"),
                Name = "grOwner",
                CompanyId = defaultCompanyId,
                Type = RoleType.Owner
            };

            var accountingFirmManager = new Role()
            {
                Id = Guid.Parse("d5757f97-b9cd-491a-8bf1-0d4a443e435a"),
                Name = "grAccountingFirmManager",
                CompanyId = defaultCompanyId,
                Type = RoleType.Accounting
            };

            var accountingFirmSpeacialist = new Role()
            {
                Id = Guid.Parse("1e85b254-6fc7-4a01-9ad0-e8d9bdb39def"),
                Name = "grAccountingFirmSpecialist",
                CompanyId = defaultCompanyId,
                Type = RoleType.Accounting
            };

            var companyManager = new Role()
            {
                Id = Guid.Parse("14eaaca4-a5e8-47dd-8942-46ebfbd3e3ae"),
                Name = "grCompanyManager",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var humanResources = new Role()
            {
                Id = Guid.Parse("dc221982-8016-4215-81af-58c09783d1f8"),
                Name = "grHumanResources",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var accountant = new Role()
            {
                Id = Guid.Parse("ea42d985-92a3-4378-b895-87a9f4550e13"),
                Name = "grAccountant",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var itSupport = new Role()
            {
                Id = Guid.Parse("e4a7048a-8045-45ed-bee2-cee5ac65c574"),
                Name = "grITSupport",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var departmentManager = new Role()
            {
                Id = Guid.Parse("5d8c1a1c-**************-ca65cef3a90b"),
                Name = "grDepartmentManager",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var teamLead = new Role()
            {
                Id = Guid.Parse("af9e26b5-c21a-4185-8b4e-4d6276b5c6fe"),
                Name = "grTeamLead",
                CompanyId = defaultCompanyId,
                Type = RoleType.Management
            };

            var employee = new Role()
            {
                Id = Guid.Parse("8168bb92-0887-4451-a9d2-************"),
                Name = "grEmployee",
                CompanyId = defaultCompanyId,
                Type = RoleType.Employee
            };

            var creator = new Role()
            {
                Id = Guid.Parse("1EF8D46A-54EB-4414-AEFA-A0CDA0FD8406"),
                Name = "grCreator",
                CompanyId = defaultCompanyId,
                Type = RoleType.Creator
            };

            var generalRoles = new List<Role>
            {
                owner,
                accountingFirmManager,
                accountingFirmSpeacialist,
                companyManager,
                humanResources,
                accountant,
                itSupport,
                departmentManager,
                teamLead,
                employee,
                creator
            };

            modelBuilder.Entity<Role>().HasData(generalRoles);

            var permissionRoles = new List<PermissionRole>();

            foreach (var role in generalRoles.Where(gr=>gr.Name != "grEmployee"))
            {
                foreach (var permission in data)
                {
                    if (permission.Name == DefaultPermissions.Companies.Delete && role.Name != "grCreator")
                        continue;

                    if (permission.Name == DefaultPermissions.Companies.Leave && role.Name == "grOwner")
                        continue;

                    permissionRoles.Add(new PermissionRole { PermissionId = permission.Id, RoleId = role.Id, Permission = null, Role = null });
                }
            }

            var employeeRole = generalRoles.FirstOrDefault(gr => gr.Name == "grEmployee");
            permissionRoles.Add(new PermissionRole { PermissionId = data.FirstOrDefault(D => D.Name == DefaultPermissions.Employees.Read).Id, RoleId = employeeRole.Id, Permission = null, Role = null });
            permissionRoles.Add(new PermissionRole { PermissionId = data.FirstOrDefault(D => D.Name == DefaultPermissions.Attendances.Read).Id, RoleId = employeeRole.Id, Permission = null, Role = null });

            //For test purposes
            var attendencePermission = data.FirstOrDefault(D => D.Name == DefaultPermissions.Attendances.Write).Id;
            var itWritePermission = permissionRoles.FirstOrDefault(p => p.RoleId == Guid.Parse(DefaultWorktimeRole.ITSupport) && p.PermissionId == attendencePermission);
            permissionRoles.Remove(itWritePermission);

            modelBuilder.Entity<PermissionRole>().HasData(permissionRoles);
        }

        private static void SeedNotificationTypes(ModelBuilder modelBuilder)
        {
            var notificationGroups = new List<NotificationGroup>
            {
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid("Absences"),
                    Name = "Absences",
                },
                 new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid("Employees"),
                    Name = "Employees",
                },
                 new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid("Hospitals"),
                    Name = "Hospitals",
                },
                 new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid("PendingEmployees"),
                    Name = "PendingEmployees",
                },
                 new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid("Payrolls"),
                    Name = "Payrolls",
                }
            };

            var notificationTypes = new List<NotificationType>
            {
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.AddedByEmployee.Email),
                    Name = NotificationsName.Absences.AddedByEmployee.Email,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.AddedByEmployee.Push),
                    Name = NotificationsName.Absences.AddedByEmployee.Push,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByEmployee.Push),
                    Name = NotificationsName.Absences.EditedByEmployee.Push,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByEmployee.Email),
                    Name = NotificationsName.Absences.EditedByEmployee.Email,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Push),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Email),
                    Name = NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Push),
                    Name = NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByAdmin.Email),
                    Name = NotificationsName.Absences.EditedByAdmin.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByAdmin.Push),
                    Name = NotificationsName.Absences.EditedByAdmin.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Approved.Email),
                    Name = NotificationsName.Absences.Approved.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Approved.Push),
                    Name = NotificationsName.Absences.Approved.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Declined.Email),
                    Name = NotificationsName.Absences.Declined.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Declined.Push),
                    Name = NotificationsName.Absences.Declined.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteRequestByEmployee.Email),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Absences.DeleteRequestByEmployee.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteRequestByEmployee.Push),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Absences.DeleteRequestByEmployee.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Edit.Email),
                    Name = NotificationsName.Absences.Edit.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Edit.Push),
                    Name = NotificationsName.Absences.Edit.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Delete.Email),
                    Name = NotificationsName.Absences.Delete.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Delete.Push),
                    Name = NotificationsName.Absences.Delete.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteByAdmin.Email),
                    Name = NotificationsName.Absences.DeleteByAdmin.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteByAdmin.Push),
                    Name = NotificationsName.Absences.DeleteByAdmin.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteDeclined.Email),
                    Name = NotificationsName.Absences.DeleteDeclined.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteDeclined.Push),
                    Name = NotificationsName.Absences.DeleteDeclined.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Absences"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.AddedByEmployee.Email),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Hospitals.AddedByEmployee.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Hospitals"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.AddedByEmployee.Push),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Notifications.Absences),
                    Name = NotificationsName.Hospitals.AddedByEmployee.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Hospitals"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.Approved.Email),
                    Name = NotificationsName.Hospitals.Approved.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Hospitals"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.Approved.Push),
                    Name = NotificationsName.Hospitals.Approved.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Hospitals"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.PendingEmployees.Updated.Push),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Read),
                    Name = NotificationsName.PendingEmployees.Updated.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("PendingEmployees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Payrolls.Added.Push),
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Read),
                    Name = NotificationsName.Payrolls.Added.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Payrolls"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditConfirmed.Push),
                    Name = NotificationsName.Employees.EditConfirmed.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditConfirmed.Email),
                    Name = NotificationsName.Employees.EditConfirmed.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditDeclined.Push),
                    Name = NotificationsName.Employees.EditDeclined.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditDeclined.Email),
                    Name = NotificationsName.Employees.EditDeclined.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByEmployee.Push),
                    Name = NotificationsName.Employees.EditedByEmployee.Push,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Write),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByEmployee.Email),
                    Name = NotificationsName.Employees.EditedByEmployee.Email,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Write),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByAdmin.Push),
                    Name = NotificationsName.Employees.EditedByAdmin.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByAdmin.Email),
                    Name = NotificationsName.Employees.EditedByAdmin.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByEmployee.Push),
                    Name = NotificationsName.Employees.AddressesEditedByEmployee.Push,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Write),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByEmployee.Email),
                    Name = NotificationsName.Employees.AddressesEditedByEmployee.Email,
                    PermissionId = GuidGeneratorUtility.GenerateGuid(DefaultPermissions.Employees.Write),
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByAdmin.Push),
                    Name = NotificationsName.Employees.AddressesEditedByAdmin.Push,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
                new()
                {
                    Id = GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByAdmin.Email),
                    Name = NotificationsName.Employees.AddressesEditedByAdmin.Email,
                    NotificationGroupId = GuidGeneratorUtility.GenerateGuid("Employees"),
                },
            };

            modelBuilder.Entity<NotificationGroup>().HasData(notificationGroups);
            modelBuilder.Entity<NotificationType>().HasData(notificationTypes);
        }

        static IEnumerable<string> GetNames(Type type)
        {
            var props = type.GetProperties()
                .Select(prop => prop.GetValue(null)?.ToString())
                .Where(value => !string.IsNullOrEmpty(value))
                .ToList();

            var nested = type.GetNestedTypes().SelectMany(GetNames);

            return props.Concat(nested);
        }
    }
}