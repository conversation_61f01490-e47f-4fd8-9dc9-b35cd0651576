import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";
import { AnnexPayrollSummaryDTO } from "./AnnexPayrollSummaryDTO";
import { ContractDTO } from "./ContractDTO";
import { LengthOfServiceDTO } from "./LengthOfService";

export interface PayrollSummaryDTO {
  contractNumber?: string;
  structureLevelId: string;
  structureLevelName: string;
  position?: NomenclatureDTO;
  contractReason?: NomenclatureDTO;
  incomeType?: NomenclatureDTO;
  contractType: ContractDTO;
  permanentContractType?: NomenclatureDTO;
  dailyWorktime: number;
  workplace: string;
  fromDate: Date;
  toDate: Date;
  lastModifyDate: Date | null;
  contractTermDate: Date | null;
  contractDate: Date | null;
  contractEndDate: Date | null;
  contractTerminationDate: Date | null;
  note: string;
  kid: string;
  ekatte: string;
  nkpd: string;
  additionalTerms: string;
  annexPayrolls?: AnnexPayrollSummaryDTO[];
  professionalЕxperienceInCompany?: LengthOfServiceDTO;
  professionalЕxperience?: LengthOfServiceDTO;
  workExperience?: LengthOfServiceDTO;
}
