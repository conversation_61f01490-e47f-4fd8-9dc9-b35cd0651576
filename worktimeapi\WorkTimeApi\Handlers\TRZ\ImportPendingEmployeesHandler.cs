﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.Notifications.SignalR;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.TRZ;

namespace WorkTimeApi.Handlers.TRZ
{
    public class ImportPendingEmployeesHandler(ITRZService trzService,
        IMediator mediator, GlobalUser globalUser,
        IAbsencesService absencesService) : IRequestHandler<ImportPendingEmployeesRequest, IResult>
    {
        public async Task<IResult> Handle(ImportPendingEmployeesRequest request, CancellationToken cancellationToken)
        {
            var result = await trzService.ImportTRZEmployeesAsync(request.PayrollIds, request.CompanyId);
            var events = await absencesService.GetAbsencesForCurrentPayrollsAsync(request.PayrollIds);

            var notificationUpdatedAbsences = new AddPayrollsNotification([.. result], request.CompanyId, globalUser.Id);

            mediator.Publish(notificationUpdatedAbsences, cancellationToken);
            await trzService.DeletePendingPayrollsAsync(request.PayrollIds);

            return Results.Ok(result);
        }
    }
}
