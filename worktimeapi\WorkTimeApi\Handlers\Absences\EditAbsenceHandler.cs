using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Absences
{
    public class EditAbsenceHandler(IAbsencesService absencesService,
        GlobalEmployee globalEmployee,
        GlobalUser globalUser,
        IMediator mediator) : IRequestHandler<EditAbsenceRequest, IResult>
    {
        public async Task<IResult> Handle(EditAbsenceRequest request, CancellationToken cancellationToken)
        {
            var isCommentChangeOnly = await absencesService.IsCommentChangeOnly(request);
            var result = await absencesService.EditAbsenceAsync(request, isCommentChangeOnly);
            if (result.IsError)
                return Results.BadRequest(result.ValidationError);

            var isActiveAbsence = result.ValidationWarning.ValidationWarnings.Any(ve => ve == (int)AbsenceValidationErrorsEnum.AbsenceAlreadyStarted);
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(request.Id, ((EventType)request.TypeIdentifier).IsHospital());
            if (isCommentChangeOnly)
            {
                var notificationEditedByEmployee = new AbsenceEditedByEmployeeNotification(result.Value.NewAbsence, globalEmployee.CompanyId, globalUser.Id, isActiveAbsence, $"{employee.FirstName} {employee.LastName}");
                mediator.Publish(notificationEditedByEmployee, cancellationToken);
            }
            else
            {
                var notificationEdit = new AbsenceEditNotification(result.Value.NewAbsence, globalEmployee.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}", employee.WorkTimeId, result.Value.OldAbsence, isActiveAbsence);
                mediator.Publish(notificationEdit, cancellationToken);
            }

            return Results.Ok(result);
        }
    }
}
