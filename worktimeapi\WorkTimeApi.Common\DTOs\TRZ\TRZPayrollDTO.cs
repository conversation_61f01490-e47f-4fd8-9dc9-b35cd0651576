﻿using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.DTOs.TRZ
{
    public class TRZPayrollDTO
    {
        public int TRZPayrollId { get; set; }

        public Guid? WorkTimeId { get; set; }

        public int? AnnexPayrollId { get; set; }

        public Guid? MainPayrollWorktimeGuid { get; set; }

        public string? ContractNumber { get; set; }

        public int? AnnexChanges { get; set; }

        public InsuranceTerminationCode? CodeOfInsuranceTermination { get; set; }

        public TerminationReason? TerminationReason { get; set; }

        public string? AnnexPayrollNumber { get; set; }

        public DateTime? AnnexPayrollFromDate { get; set; }

        public DateTime? AnnexPayrollToDate { get; set; }

        public PermanentContractType PermanentContractType { get; set; }

        public int? PayrollWorkTime { get; set; }

        public decimal? PayrollSalary { get; set; }

        public DateTime? ContractDate { get; set; }

        public DateTime? ContrractEndDate { get; set; }

        public DateTime? ContrractTermDate { get; set; }

        public string? Note { get; set; }

        public bool IsDeleted { get; set; }

        public string? Location { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public int? MODCode { get; set; }

        public bool? IsFromEducationSystem { get; set; }

        public int? NotificationCorrectionCode { get; set; }

        public NotificationDocumentType? NotificationDocumentType { get; set; }

        public string? Occupation { get; set; }

        public string? EKATTECode { get; set; }

        public NomenclatureDTO? IncomeType { get; set; }

        public required TRZDepartmentDTO DepartmentGroup { get; set; }

        public NomenclatureDTO? ContractReason { get; set; }

        public NomenclatureDTO? Contract { get; set; }

        public NomenclatureDTO? Position { get; set; }

        public int AnnualPaidLeave { get; set; }

        public int AdditionalAnnualPaidLeave { get; set; }

        public int AnnualPaidLeavePastYears { get; set; }

        public ICollection<TRZKidDTO>? KIDs { get; set; }

        public ICollection<TRZKpdDTO>? KPDs { get; set; }

        public ICollection<TRZEventDTO>? PendingEvents { get; set; }

        public ICollection<AnnexPayrollDTO>? AnnexPayrolls { get; set; }
    }
}
