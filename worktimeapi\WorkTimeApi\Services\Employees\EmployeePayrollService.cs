﻿using AutoMapper;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Database.Repositories.Interfaces.TRZ;
using WorkTimeApi.Database.Repositories.Interfaces.Users;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Services.Employees
{
    public class EmployeePayrollService(IMediator mediator, IEmployeesListMapper employeesListMapper,
        IEmployeesRepository employeesRepository, IUserRepository userRepository,
        IMapper mapper, ITRZRepository trzRepository) : IEmployeePayrollService
    {
        public async Task<IEnumerable<EmployeePayrollDTO>> LoadEmployeePayrollListAsync(LoadEmployeePayrollRequest loadEmployeePayrollRequest)
        {
            return employeesListMapper.MapEmployeesWithPayrollDetails(await employeesRepository.GetEmployeePayrollListAsync(loadEmployeePayrollRequest.CompanyId));
        }

        public async Task<IEnumerable<EmployeePayrollDTO>> LoadPendingEmployeePayrollListAsync(LoadPendingEmployeePayrollRequest loadPeningEmployeePayrollRequest)
        {
            return employeesListMapper.MapPendingEmployeesWithPayrollDetails(await trzRepository.GetPendingEmployeePayrollListAsync(loadPeningEmployeePayrollRequest.CompanyId));
        }

        public async Task<IEnumerable<EmployeePayrollDTO>> LoadUserEmployeePayrollListAsync(Guid userId, Guid companyId)
        {
            return employeesListMapper.MapEmployeesWithPayrollDetails(await employeesRepository.GetUserEmployeePayrollListAsync(userId, companyId));
        }

        public async Task<EmployeePayrollDTO> AddNewEmployeePayrollAsync(AddNewEmployeePayrollRequest request)
        {
            var user = new User()
            {
                Id = request.NewUserId,
                FirstName = request.PersonalData.FirstName,
                SecondName = request.PersonalData.SecondName,
                LastName = request.PersonalData.LastName,
                Email = request.PersonalData.Email,
                Code = request.PersonalData.Code,
                CodePassword = request.PersonalData.CodePassword,
            };

            await userRepository.AddUserAsync(user);

            var employee = new Employee()
            {
                FirstName = request.PersonalData.FirstName,
                SecondName = request.PersonalData.SecondName,
                LastName = request.PersonalData.LastName,
                EGN = request.PersonalData.EGN,
                Email = request.PersonalData.Email,
                WorkEmail = request.AddressData?.WorkEmail,
                Phone = request.AddressData?.Phone,
                WorkPhone = request.AddressData?.WorkPhone,
                IDNumber = request.IdentityCardData?.IdNumber,
                IDIssuedFrom = request.IdentityCardData?.IssuedBy,
                IDIssueDate = request.IdentityCardData?.IssuedOn,
                BirthDate = request.PersonalData.BirthDate,
                BirthPlace = request.PersonalData.BirthPlace,
                Gender = request.IdentityCardData.Gender,
                Citizenship = request.IdentityCardData?.Citizenship,
                CompanyId = request.CompanyId,
                Status = EmployeeCompanyStatus.Active,
                UserId = request.NewUserId,
            };

            var identityCardAddress = request.IdentityCardData?.IdAddress;
            if (identityCardAddress != null)
            {
                var identityAddress = new Address()
                {
                    Purpose = AddressPurpose.IdentityCard,
                    Block = identityCardAddress.Block,
                    Street = identityCardAddress.Street,
                    Apartment = identityCardAddress.Apartment,
                    PostalCode = identityCardAddress.PostalCode,
                    Neighborhood = identityCardAddress.Neighborhood,
                    Phone = identityCardAddress.Phone,
                    WorkPhone = identityCardAddress.WorkPhone,
                    Email = identityCardAddress.Email,
                    WorkEmail = identityCardAddress.WorkEmail,
                    CityName = identityCardAddress.CityName,
                    DistrictName = identityCardAddress.DistrictName,
                    MunicipalityName = identityCardAddress.MunicipalityName,
                };

                if (identityCardAddress.Country != null && Enum.IsDefined(typeof(Country), identityCardAddress.Country.Identifier))
                {
                    identityAddress.Country = (Country)identityCardAddress.Country.Identifier;
                }

                employee.EmployeeAddresses.Add(new EmployeeAddress() { Employee = employee, Address = identityAddress });
            }

            var address = new Address()
            {
                Purpose = AddressPurpose.ForContact,
                Block = request.AddressData.Block,
                Street = request.AddressData.Street,
                Apartment = request.AddressData.Apartment,
                PostalCode = request.AddressData.PostalCode,
                Neighborhood = request.AddressData.Neighborhood,
                Phone = request.AddressData.Phone,
                WorkPhone = request.AddressData.WorkPhone,
                Email = request.AddressData.Email,
                WorkEmail = request.AddressData.WorkEmail,
                CityName = request.AddressData.CityName,
                DistrictName = request.AddressData.DistrictName,
                MunicipalityName = request.AddressData.MunicipalityName,
            };

            if (request.AddressData.Country != null && Enum.IsDefined(typeof(Country), request.AddressData.Country.Identifier))
            {
                address.Country = (Country)request.AddressData.Country.Identifier;
            }

            if (request.AddressData.City != null)
            {
                address.CityId = request.AddressData.City.Id;
            }

            if (request.AddressData.District is not null)
            {
                address.DistrictId = request.AddressData.District.Id;
            }

            if (request.AddressData.Municipality is not null)
            {
                address.MunicipalityId = request.AddressData.Municipality.Id;
            }

            employee.EmployeeAddresses.Add(new EmployeeAddress() { Employee = employee, Address = address });

            employee.EmployeeRoles.Add(new RoleEmployee()
            {
                EmployeeId = employee.Id,
                RoleId = new Guid(DefaultWorktimeRole.Employee)
            });

           var addedEmployee = await employeesRepository.AddEmployeeAsync(employee);

            mediator.Publish(new AddDefaultNotificationSettings(addedEmployee.Id, new Guid(DefaultWorktimeRole.Employee)));

            var employeePayrollDTO = employeesListMapper.CreateEmployeePayrollDTOBase(employee, employee.Payrolls.FirstOrDefault(), null);

            return employeePayrollDTO;
        }
    }
}
