﻿
using WorkTimeApi.Database.Models;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using WorkTimeApi.Database.GuidGenerator;

namespace WorkTimeApi.Database.FileReaders
{
    public class NKPDExcelReader
    {
        public List<KPD> ReadExcelFile(int year)
        {
            var nkpdList = new List<KPD>();
            string fileName = $"NKPD_{year}.xlsx";

            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SeedData", fileName);

            if (filePath is null)
            {
                return nkpdList;
            }

            using SpreadsheetDocument spreadSheetDocument = SpreadsheetDocument.Open(filePath, false);

            if (spreadSheetDocument is null)
            {
                return nkpdList;
            }

            WorkbookPart? workbookPart = spreadSheetDocument?.WorkbookPart;
            IEnumerable<Sheet>? sheets = workbookPart?.Workbook?.GetFirstChild<Sheets>()?.Elements<Sheet>();

            string? relationshipId = sheets?.First().Id?.Value;

            if (relationshipId is null)
            {
                return nkpdList;
            }

            WorksheetPart? worksheetPart = (WorksheetPart?)workbookPart?.GetPartById(relationshipId);
            Worksheet? workSheet = worksheetPart?.Worksheet;
            SheetData? sheetData = workSheet?.GetFirstChild<SheetData>();
            IEnumerable<Row>? rows = sheetData?.Descendants<Row>().Skip(1) ?? new List<Row>();

            if (spreadSheetDocument is null)
            {
                return nkpdList;
            }

            foreach (Row row in rows)
            {
                var cells = row.Descendants<Cell>().ToList();
                var description = cells.Count > 1 && cells.Count < 3 ? GetCellValue(spreadSheetDocument, cells.ElementAtOrDefault(1)) : GetCellValue(spreadSheetDocument, cells.ElementAtOrDefault(2));
                string key = row.RowIndex +  fileName;

                var nkpd = new KPD
                {
                    Id = GuidGeneratorUtility.GenerateGuid(key),
                    Section = GetCellValue(spreadSheetDocument, cells.ElementAtOrDefault(0)),
                    Code = cells.Count > 1 && cells.Count < 3 ? "" : GetCellValue(spreadSheetDocument, cells.ElementAtOrDefault(1)),
                    Description = description,
                    ValidFrom = new DateTime(year,1,1),
                };

                nkpdList.Add(nkpd);
            }

            return nkpdList;
        }

        private static string? GetCellValue(SpreadsheetDocument document, Cell cell)
        {
            if (cell == null || cell.CellValue == null)
            {
                return null;
            }

            SharedStringTablePart stringTablePart = document?.WorkbookPart?.SharedStringTablePart;
            string value = cell.CellValue.InnerXml;

            if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString)
            {
                return stringTablePart?.SharedStringTable.ChildElements[Int32.Parse(value)].InnerText;
            }
            else
            {
                return value;
            }
        }
    }
}
