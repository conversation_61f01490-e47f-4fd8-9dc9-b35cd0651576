import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { confirmEmail } from "../../services/authentication/authenticationService";
import Translator from "../../services/language/Translator";
import SuccessIcon from "../../assets/images/success-icon.svg";

const ConfirmEmail = () => {
  const [isConfirmed, setIsConfirmed] = useState<boolean>(true);
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const userId = queryParams.get("userId");
  const code = queryParams.get("code");

  useEffect(() => {
    if (userId && code) {
      confirmEmail(userId, code)
        .then(() => {
          setIsConfirmed(true);
        })
        .catch(() => {
          setIsConfirmed(false);
        });
    }
  }, [userId, code]);

  const renderContent = () => {
    if (isConfirmed === undefined) {
      return (
        <div
          style={{
            fontSize: "18px",
            lineHeight: "24px",
            fontWeight: "normal",
            letterSpacing: "0px",
            color: "var(--confirmation-email-sent-color)",
            opacity: "1",
          }}
        >
          <Translator getString="Confirming E-mail" />
        </div>
      );
    } else if (isConfirmed) {
      return (
        <>
          <div
            style={{
              marginBottom: "0.8rem",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              src={SuccessIcon}
              alt="Success"
              style={{
                width: "48px",
                height: "48px",
              }}
            />
          </div>
          <div
            style={{
              fontSize: "18px",
              lineHeight: "24px",
              fontWeight: "normal",
              letterSpacing: "0px",
              color: "var(--confirmation-email-sent-color)",
              opacity: "1",
            }}
          >
            <Translator getString="E-mail confirmed successfully" />
          </div>
        </>
      );
    } else {
      return (
        <div
          style={{
            fontSize: "18px",
            lineHeight: "24px",
            fontWeight: "normal",
            letterSpacing: "0px",
            color: "var(--error-color)",
            opacity: "1",
          }}
        >
          <Translator getString="E-mail was not confirmed!" />
        </div>
      );
    }
  };

  return (
    <div
      style={{
        minHeight: "calc(100vh - 100px)",
        backgroundColor: "transparent",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          backgroundColor: "transparent",
          textAlign: "center",
          maxWidth: "500px",
          width: "100%",
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 1,
        }}
      >
        {renderContent()}
      </div>
    </div>
  );
};

export default ConfirmEmail;
