import { useContext, useEffect, useState } from "react";
import Image from "../components/Image";
import { AvailableLanguages } from "../services/language/constants";
import { LanguageContext } from "../services/language/LanguageContext";
import styled from "styled-components";

import languageEN from "/src/assets/images/languages/languageEN.svg";
import languageBG from "/src/assets/images/languages/languageBG.svg";
import languageBG_hover from "/src/assets/images/languages/languageBG_hover.svg";
import languageEN_hover from "/src/assets/images/languages/languageEN_hover.svg";
import React from "react";

const StyledImage = styled(Image)`
  margin-right: 0.6rem;
  user-select: none;
`;

const LanguageSelector = () => {
  const { userLanguage, userLanguageChange } = useContext(LanguageContext);

  const handleLanguageChange = () => {
    const nextLanguage: AvailableLanguages =
      userLanguage === "bg" ? "en" : "bg";
    userLanguageChange(nextLanguage);
  };

  const languageImages: Record<string, { default: string; hover: string }> = {
    bg: { default: languageBG, hover: languageBG_hover },
    en: { default: languageEN, hover: languageEN_hover },
  };

  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    let defaultLanguage = window.localStorage.getItem(
      "selected-language"
    ) as AvailableLanguages;

    if (!defaultLanguage) {
      defaultLanguage = "bg";
    }

    userLanguageChange(defaultLanguage);
  }, [userLanguageChange]);

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return (
    <StyledImage
      alt="LanguageImage"
      size="small"
      src={
        isHovered
          ? languageImages[userLanguage].hover
          : languageImages[userLanguage].default
      }
      onClick={handleLanguageChange}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-testid="language-selector-current-language"
    />
  );
};

export default LanguageSelector;
