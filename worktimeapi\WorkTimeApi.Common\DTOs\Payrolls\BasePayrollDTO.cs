﻿using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.DTOs.Payrolls
{
    public class BasePayrollDTO
    {
        public Guid WorkTimeId { get; set; }

        public int? TRZPayrollId { get; set; }

        public Guid CompanyId { get; set; }

        public InsuranceTerminationCode? CodeOfInsuranceTermination { get; set; }

        public TerminationReason? TerminationReason { get; set; }

        public int? PayrollWorkTime { get; set; }

        public DateTime? ContractDate { get; set; }

        public DateTime? ContrractEndDate { get; set; }

        public DateTime? ContrractTermDate { get; set; }

        public string? Note { get; set; }

        public bool IsDeleted { get; set; }

        public string? Location { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public bool IsFromEducationSystem { get; set; }

        public int NotificationCorrectionCode { get; set; }

        public NotificationDocumentType? NotificationDocumentType { get; set; }

        public PermanentContractType? PermanentContractType { get; set; }

        public NomenclatureDTO? ContractReason { get; set; }

        public NomenclatureDTO? Contract { get; set; }

        public string? Occupation { get; set; }

        public NomenclatureDTO? Position { get; set; }

        public int? MODCode { get; set; }

        public NomenclatureDTO? IncomeType { get; set; }

        public string? EKATTECode { get; set; }

        public DateTime UploadDate { get; set; }

        public PayrollCategories PayrollCategory { get; set; }

        public TypeOfAppointment? ContractType { get; set; }

        public Guid StructureLevelId { get; set; }

        public int AnnualPaidLeave { get; set; }

        public int AdditionalAnnualPaidLeave { get; set; }

        public int AnnualPaidLeavePastYears { get; set; }

        public List<KidDTO> Kids { get; set; } = [];

        public List<KpdDTO> Kpds { get; set; } = [];

    }
}
