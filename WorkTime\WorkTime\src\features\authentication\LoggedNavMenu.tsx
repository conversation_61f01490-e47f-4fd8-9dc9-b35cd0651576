import React, { useContext, useEffect } from "react";
import styled from "styled-components";
import { useLocation } from "react-router-dom";
import { useCompany } from "../companies/CompanyContext";
import ProfileImage from "../../assets/images/menu/profile.png";
import Label from "../../components/Inputs/Label";
import Menu from "../Menu";
import { useMenu } from "../MenuContext";
import Container from "../../components/Container";
import Notifications from "../notifications/notifications";
import { useNotificationDropdown } from "../notifications/NotificationContext";
import NotificationsButton from "../../components/NotificationsButton/NotificationsButton";
import LanguageSelector from "../LanguageSelector";

const Image = styled.img`
  cursor: pointer;
`;

const CompanyLabel = styled(Label)`
  color: var(--company-name-label);
  cursor: pointer;
`;

const Wrapper = styled(Container)`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const NotificationWrapper = styled(Container)`
  position: relative;
  display: inline-block;
  margin: 0.6rem;
`;

const NotificationDropdown = styled(Container)`
  padding: 1rem 1rem 0.3rem 1rem;
  position: absolute;
  right: -5rem;
  top: 3.7rem;
  z-index: 1000;
  background: white;
  width: 28rem;
  border-radius: 0rem 0 1.8rem 1.8rem;
  box-shadow: 0 0.25rem 0.8rem rgba(0, 0, 0, 0.15);
`;

interface Props {
  onProfileClick: () => void;
}

const LoggedNavMenu = ({ onProfileClick }: Props) => {
  const { company } = useCompany();
  const { isOpen, toggleMenu, changeView } = useMenu();
  const location = useLocation();
  const {
    isNotificationsOpen,
    toggleNotifications,
    notificationDropdownRef,
    unseenCount,
  } = useNotificationDropdown();

  const handleProfileClick = () => {
    changeView("profile", "other");
    onProfileClick();
  };

  const handleMenuToggle = () => {
    changeView("myCompanies", "companyLabel");
    toggleMenu();
  };

  return (
    <Wrapper data-testid="wrapper">
      {location.pathname !== "/" && (
        <CompanyLabel data-testid="company-label" onClick={handleMenuToggle}>
          {company.name ?? ""}
        </CompanyLabel>
      )}
      <NotificationWrapper>
        <NotificationsButton
          isOpen={isNotificationsOpen}
          unseenCount={unseenCount}
          onClick={toggleNotifications}
        />
        {isNotificationsOpen && (
          <NotificationDropdown ref={notificationDropdownRef}>
            <Notifications />
          </NotificationDropdown>
        )}
      </NotificationWrapper>
      <LanguageSelector data-testid="language-selector" />
      <Image
        data-testid="profile-image"
        onClick={handleProfileClick}
        src={ProfileImage}
      />
      <Menu
        data-testid="menu"
        isOpen={isOpen}
        onToggleMenu={handleMenuToggle}
      />
    </Wrapper>
  );
};

export default LoggedNavMenu;
