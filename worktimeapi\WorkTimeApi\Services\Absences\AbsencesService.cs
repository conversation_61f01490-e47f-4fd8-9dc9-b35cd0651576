﻿using AutoMapper;
using AutoMapper.Internal;
using Gateway.Common.Globals;
using Gateway.Common.Results;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Absences;
using WorkTimeApi.Database.Services.Interfaces;
using WorkTimeApi.Models.Results;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Validators.Interfaces;
using static WorkTimeApi.Common.NotificationsName;

namespace WorkTimeApi.Services.Absences
{
    public class AbsencesService(IMapper mapper,
        IAbsencesRepository absencesRepository,
        ICalendarService calendarService,
        GlobalUser globalUser,
        GlobalEmployee globalEmployee,
        IAbsencesValidator absencesValidator) : IAbsencesService
    {
        public async Task<Result<IEnumerable<AbsenceHospitalDTO>>> AddAbsencesAsync(IEnumerable<EventDTO> eventsDTO)
        {
            var validationResult = new ValidationResult();
            foreach (var eventDTO in eventsDTO)
            {
                if ((!eventsDTO.Any() || !eventDTO.EventType.IsHospital()) && eventDTO.WorkTimePayrollId.HasValue)
                    validationResult = await absencesValidator.ValidateAbsencesAsync(eventDTO.WorkTimePayrollId.Value, eventsDTO);

                if (validationResult.HasValidationErrors)
                    return validationResult;

                if (!validationResult.HasValidationWarnings && eventDTO.WorkTimePayrollId.HasValue)
                    validationResult = await absencesValidator.ValidateHospitalAsync(eventDTO.WorkTimePayrollId.Value, eventsDTO);

                eventDTO.Duration = await calendarService.CalculateEventDuration(eventDTO.StartDate, eventDTO.EndDate);
            }

            var leaves = mapper.Map<List<Absence>>(eventsDTO.Where(e => !e.EventType.IsHospital()));
            var hospitals = mapper.Map<List<Hospital>>(eventsDTO.Where(e => e.EventType.IsHospital()));

            if (validationResult.ValidationWarnings.Count == 1 && validationResult.ValidationWarnings.FirstOrDefault() == (int)AbsenceValidationErrorsEnum.AbsenceExistsForSickLeave)
            {
                hospitals.ForEach(h => h.IsOverlapping = true);
            }

            await absencesRepository.AddAbsencesAsync(leaves);
            await absencesRepository.AddAbsencesAsync(hospitals);

            var absences = new List<AbsenceHospitalDTO>();

            var leaveDTOs = mapper.Map<List<AbsenceHospitalDTO>>(leaves);
            var hospitalDTOs = mapper.Map<List<AbsenceHospitalDTO>>(hospitals);

            return new Result<IEnumerable<AbsenceHospitalDTO>>(absences.Concat(leaveDTOs).Concat(hospitalDTOs), validationResult);
        }

        public async Task<AbsenceHospitalDTO> DeletePersonalAbsenceAsync(Guid absenceId, Guid payrollId, bool isHospital)
        {
            if (isHospital)
            {
                var hospital = await absencesRepository.GetByIdAsync<Hospital>(absenceId) ?? throw new Exception("There is no hospital with this ID");
                switch (hospital.Status)
                {
                    case AbsenceStatus.Pending:
                        hospital = await absencesRepository.DeletePersonalAbsenceAsync<Hospital>(absenceId, payrollId, globalUser.Id);
                        break;
                    case AbsenceStatus.DeletedByUserAfterApproval:
                        hospital.Status = AbsenceStatus.Approved;
                        await absencesRepository.UpdateAbsenceAsync(hospital);
                        break;
                    default:
                        hospital.Status = AbsenceStatus.DeletedByUserAfterApproval;
                        await absencesRepository.UpdateAbsenceAsync(hospital);
                        break;
                }

                return mapper.Map<AbsenceHospitalDTO>(hospital);
            }
            else
            {
                var absence = await absencesRepository.GetByIdAsync<Absence>(absenceId) ?? throw new Exception("There is no absence with this ID");
                switch (absence.Status)
                {
                    case AbsenceStatus.Pending:
                        absence = await absencesRepository.DeletePersonalAbsenceAsync<Absence>(absenceId, payrollId, globalUser.Id);
                        break;
                    case AbsenceStatus.DeletedByUserAfterApproval:
                        absence.Status = AbsenceStatus.Approved;
                        await absencesRepository.UpdateAbsenceAsync(absence);
                        break;
                    default:
                        absence.Status = AbsenceStatus.DeletedByUserAfterApproval;
                        await absencesRepository.UpdateAbsenceAsync(absence);
                        break;
                }

                return mapper.Map<AbsenceHospitalDTO>(absence);
            }
        }

        public async Task<AbsenceHospitalDTO> DeleteAbsenceAsync(Guid absenceId, Guid payrollId, bool isHospital)
        {
            if (isHospital)
            {
                var hospital = await absencesRepository.GetByIdAsync<Hospital>(absenceId);
                if (hospital is null || hospital.Payroll.Employee.CompanyId != globalEmployee.CompanyId)
                    throw new ArgumentException("Невалидно Id на болничен за тази компания!");

                hospital.Status =
                    hospital.Status == AbsenceStatus.DeletedByUserAfterApproval
                    ? AbsenceStatus.Approved
                    : AbsenceStatus.DeletedByAdmin;

                if (hospital.Status == AbsenceStatus.DeletedByAdmin)
                    hospital.ExportStatus = AbsenceExportStatus.Pending;

                hospital = await absencesRepository.UpdateAbsenceAsync(hospital);

                return mapper.Map<AbsenceHospitalDTO>(hospital);
            }
            else
            {
                var absence = await absencesRepository.GetByIdAsync<Absence>(absenceId);
                if (absence is null || absence.Payroll.Employee.CompanyId != globalEmployee.CompanyId)
                    throw new ArgumentException("Невалидно Id на отпуск за тази компания!");

                absence.Status =
                    absence.Status == AbsenceStatus.DeletedByUserAfterApproval
                    ? AbsenceStatus.Approved
                    : AbsenceStatus.DeletedByAdmin;

                if (absence.Status == AbsenceStatus.DeletedByAdmin)
                    absence.ExportStatus = AbsenceExportStatus.Pending;

                absence = await absencesRepository.UpdateAbsenceAsync(absence);

                return mapper.Map<AbsenceHospitalDTO>(absence);
            }
        }

        public async Task<IEnumerable<AbsenceHospitalDTO>> GetAbsencesForCurrentPeriodAsync(Guid companyId, DateTime date)
        {
            var absences = await absencesRepository.GetAbsencesForMonthAsync<Absence>(companyId, date);
            var hospitals = await absencesRepository.GetAbsencesForMonthAsync<Hospital>(companyId, date);

            var absenceDtos = absences.Select(mapper.Map<AbsenceHospitalDTO>);

            var hospitalDtos = hospitals.Select(mapper.Map<AbsenceHospitalDTO>);

            var result = absenceDtos.Concat(hospitalDtos).ToList();

            return result;
        }

        public async Task<EmployeeDTO> GetEmployeeDTOByAbsenceIdAsync(Guid absenceId, bool isHospital)
        {
            if (isHospital)
            {
                var employee = await absencesRepository.GetEmployeeByAbsenceIdAsync<Hospital>(absenceId) ?? throw new ArgumentException("Болничният не е намерен!");

                return mapper.Map<EmployeeDTO>(employee);
            }
            else
            {
                var employee = await absencesRepository.GetEmployeeByAbsenceIdAsync<Absence>(absenceId) ?? throw new ArgumentException("Отсъствието не е намерено!");

                return mapper.Map<EmployeeDTO>(employee);
            }
        }

        public async Task<IEnumerable<EventDTO>> GetCompanyAbsencesForTRZByMonthAsync(Guid companyId, DateTime monthDate, bool includeExported)
        {
            var absences = await absencesRepository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, includeExported);
            // TO DO: За сега няма да експортираме болничните
            // var hospitals = await absencesRepository.GetAbsencesForMonthAsync<Hospital>(companyId, monthDate);

            var absenceDtos = absences.Where(a => a.AbsenceType != EventType.ПлатенКомпенсация).Select(mapper.Map<EventDTO>);
            // var hospitalDtos = hospitals.Select(mapper.Map<AbsenceHospitalDTO>);

            return [.. absenceDtos];
        }

        public async Task<IEnumerable<AbsenceHospitalDTO>> MarkAbsencesAndHospitalsExportedAsync(Guid companyId, IEnumerable<Guid> absenceIds, IEnumerable<Guid> hospitalIds)
        {
            var updatedAbsences = await absencesRepository.MarkExportedAsync<Absence>(companyId, absenceIds);
            var updatedHospitals = await absencesRepository.MarkExportedAsync<Hospital>(companyId, hospitalIds);

            var absenceDtos = updatedAbsences.Select(mapper.Map<AbsenceHospitalDTO>);
            var hospitalDtos = updatedHospitals.Select(mapper.Map<AbsenceHospitalDTO>);

            return [.. absenceDtos, .. hospitalDtos];
        }

        public async Task<(AbsenceHospitalDTO, AbsenceHospitalDTO)> UpdateAbsenceApprovalRequest(Guid absenceId, AbsenceStatus absenceStatus, bool isHospital, string? message)
        {
            if (isHospital)
            {
                if (absenceStatus == AbsenceStatus.Declined)
                    throw new ArgumentException("Болничните не могат да бъдат отказвани!");

                var hospital = await absencesRepository.GetByIdForUpdateAsync<Hospital>(absenceId);
                var hospitalOldValue = mapper.Map<AbsenceHospitalDTO>(hospital);
                hospital.Status = absenceStatus;

                if (absenceStatus == AbsenceStatus.DeletedByAdmin && hospital.ExportStatus == AbsenceExportStatus.Exported)
                    hospital.ExportStatus = AbsenceExportStatus.Pending;

                if (!string.IsNullOrEmpty(message))
                {
                    var hospitalApproval = new HospitalApproval
                    {
                        Note = message,
                        HospitalId = absenceId
                    };
                    hospital.Approvals.Add(hospitalApproval);
                }

                hospital = await absencesRepository.UpdateAbsenceAsync(hospital);

                var hospitalDTO = mapper.Map<AbsenceHospitalDTO>(hospital);
                return (hospitalOldValue, hospitalDTO);
            }
            else
            {
                var absence = await absencesRepository.GetByIdForUpdateAsync<Absence>(absenceId);
                var absencelOldValue = mapper.Map<AbsenceHospitalDTO>(absence);
                absence.Status = absenceStatus;

                if (absenceStatus == AbsenceStatus.DeletedByAdmin && absence.ExportStatus == AbsenceExportStatus.Exported)
                    absence.ExportStatus = AbsenceExportStatus.Pending;

                if (!string.IsNullOrEmpty(message))
                {
                    var absenceApproval = new AbsenceApproval
                    {
                        Note = message,
                        AbsenceId = absenceId
                    };
                    absence.Approvals.Add(absenceApproval);
                }

                absence = await absencesRepository.UpdateAbsenceAsync(absence);

                var absenceDTO = mapper.Map<AbsenceHospitalDTO>(absence);

                return (absencelOldValue, absenceDTO);
            }
        }

        public async Task<List<AbsenceHospitalDTO>> UpdateOverlapingAbsences(Guid absenceId)
        {
            var changedAbsences = await absencesRepository.UpdateOverlapingAbsences<Absence>(absenceId);

            var changedAbsencesDTO = mapper.Map<List<AbsenceHospitalDTO>>(changedAbsences);

            return changedAbsencesDTO;
        }

        public async Task<bool> IsCommentChangeOnly(EditAbsenceRequest editAbsenceRequest)
        {
            var existingAbsence = await absencesRepository.GetByIdAsync<Absence>(editAbsenceRequest.Id);

            var isCommentChangeOnly = existingAbsence?.FromDate == editAbsenceRequest.FromDate
                         && existingAbsence.ToDate == editAbsenceRequest.ToDate
                         && existingAbsence.AbsenceType == (EventType)editAbsenceRequest.TypeIdentifier
                         && existingAbsence.Reference != editAbsenceRequest.Comment;

            return isCommentChangeOnly;
        }

        public async Task<Result<EditAbsenceResult>> EditAbsenceAsync(EditAbsenceRequest editAbsenceRequest, bool isCommentChangeOnly = false)
        {
            var eventType = (EventType)editAbsenceRequest.TypeIdentifier;
            var isNewTypeHospital = eventType.IsHospital();
            var result = new EditAbsenceResult();
            var eventDto = mapper.Map<EventDTO>(editAbsenceRequest);

            var validationResult = new ValidationResult();

            if (!eventDto.EventType.IsHospital() && eventDto.WorkTimePayrollId.HasValue && !editAbsenceRequest.IsAdminEdit)
            {
                var absence = await absencesRepository.GetByIdAsync<Absence>(editAbsenceRequest.Id);
                validationResult = absencesValidator.ValidateActiveAbsence(eventDto.WorkTimePayrollId.Value, [eventDto]);
                if (absence is not null && validationResult.HasValidationErrors || validationResult.HasValidationWarnings)
                {
                    absence!.Status = AbsenceStatus.Approved;
                    eventDto.Status = AbsenceStatus.Approved;
                    await absencesRepository.UpdateAbsenceAsync(absence);

                    result.NewAbsence = mapper.Map<AbsenceHospitalDTO>(eventDto);
                    result.OldAbsence = mapper.Map<AbsenceHospitalDTO>(absence);

                    return new Result<EditAbsenceResult>(result, validationResult);
                }
            }

            if (!eventDto.EventType.IsHospital() && eventDto.WorkTimePayrollId.HasValue)
                validationResult = await absencesValidator.ValidateAbsencesAsync(eventDto.WorkTimePayrollId.Value, [eventDto]);

            if (validationResult.HasValidationErrors)
                return validationResult;

            if (!validationResult.HasValidationWarnings && eventDto.WorkTimePayrollId.HasValue)
                validationResult = await absencesValidator.ValidateHospitalAsync(eventDto.WorkTimePayrollId.Value, [eventDto]);

            var existingHospital = await absencesRepository.GetByIdAsync<Hospital>(editAbsenceRequest.Id);
            if (existingHospital != null)
            {
                var status = existingHospital.Status == AbsenceStatus.Pending ? AbsenceStatus.Pending
                    : editAbsenceRequest.IsAdminEdit ? AbsenceStatus.EditedByAdmin : AbsenceStatus.EditedByEmployee;

                existingHospital.ExportStatus = AbsenceExportStatus.Edited;

                if (isNewTypeHospital)
                {
                    var isCommentOnly = existingHospital.FromDate == editAbsenceRequest.FromDate
                        && existingHospital.ToDate == editAbsenceRequest.ToDate
                        && existingHospital.HospitalType == eventType
                        && existingHospital.SickNote != editAbsenceRequest.SickNote;

                    existingHospital.FromDate = editAbsenceRequest.FromDate;
                    existingHospital.ToDate = editAbsenceRequest.ToDate;
                    existingHospital.SickNote = editAbsenceRequest.SickNote;
                    existingHospital.HospitalType = eventType;
                    existingHospital.Status = isCommentOnly ? existingHospital.Status : status;
                    existingHospital.Duration = await calendarService.CalculateEventDuration(editAbsenceRequest.FromDate, editAbsenceRequest.ToDate);

                    var updatedHospital = await absencesRepository.UpdateAbsenceAsync(existingHospital);

                    result.NewAbsence = mapper.Map<AbsenceHospitalDTO>(updatedHospital);
                    result.OldAbsence = mapper.Map<AbsenceHospitalDTO>(existingHospital);

                    return new Result<EditAbsenceResult>(result, validationResult);
                }

                var newAbsence = new Absence
                {
                    FromDate = editAbsenceRequest.FromDate,
                    ToDate = editAbsenceRequest.ToDate,
                    Reference = editAbsenceRequest.Comment,
                    AbsenceType = eventType,
                    PayrollId = existingHospital.PayrollId,
                    Status = status
                };

                existingHospital.Status = AbsenceStatus.Deleted;
                existingHospital.ExportStatus = AbsenceExportStatus.Deleted;
                await absencesRepository.DeleteAbsenceAsync<Hospital>(existingHospital.Id);

                var addedAbsence = await absencesRepository.AddAbsenceAsync(newAbsence);

                result.OldAbsence = mapper.Map<AbsenceHospitalDTO>(existingHospital);
                result.NewAbsence = mapper.Map<AbsenceHospitalDTO>(addedAbsence);
                return new Result<EditAbsenceResult>(result, validationResult);
            }

            var existingAbsence = await absencesRepository.GetByIdAsync<Absence>(editAbsenceRequest.Id);
            if (existingAbsence != null)
            {
                result.OldAbsence = mapper.Map<AbsenceHospitalDTO>(existingAbsence);
                var status = existingAbsence.Status == AbsenceStatus.Pending ? AbsenceStatus.Pending
                    : editAbsenceRequest.IsAdminEdit ? AbsenceStatus.EditedByAdmin : AbsenceStatus.EditedByEmployee;
                existingAbsence.ExportStatus = AbsenceExportStatus.Edited;

                if (!isNewTypeHospital)
                {
                    existingAbsence.FromDate = editAbsenceRequest.FromDate;
                    existingAbsence.ToDate = editAbsenceRequest.ToDate;
                    existingAbsence.Reference = editAbsenceRequest.Comment;
                    existingAbsence.AbsenceType = eventType;
                    existingAbsence.Status = isCommentChangeOnly ? existingAbsence.Status : status;
                    existingAbsence.Duration = await calendarService.CalculateEventDuration(editAbsenceRequest.FromDate, editAbsenceRequest.ToDate);

                    var updatedAbsence = await absencesRepository.UpdateAbsenceAsync(existingAbsence);

                    result.NewAbsence = mapper.Map<AbsenceHospitalDTO>(updatedAbsence);

                    return new Result<EditAbsenceResult>(result, validationResult);
                }

                var newHospital = new Hospital
                {
                    FromDate = editAbsenceRequest.FromDate,
                    ToDate = editAbsenceRequest.ToDate,
                    SickNote = editAbsenceRequest.SickNote,
                    HospitalType = eventType,
                    PayrollId = existingAbsence.PayrollId,
                    Status = status
                };

                existingAbsence.Status = AbsenceStatus.Deleted;
                existingAbsence.ExportStatus = AbsenceExportStatus.Deleted;
                await absencesRepository.DeleteAbsenceAsync<Absence>(existingAbsence.Id);

                var addedHospital = await absencesRepository.AddAbsenceAsync(newHospital);

                result.OldAbsence = mapper.Map<AbsenceHospitalDTO>(existingAbsence);
                result.NewAbsence = mapper.Map<AbsenceHospitalDTO>(addedHospital);
                return new Result<EditAbsenceResult>(result, validationResult);
            }

            validationResult.AddError(AbsenceValidationErrorsEnum.InvalidAbsence);
            return validationResult;
        }

        public async Task<List<AbsenceHospitalDTO>> GetAbsencesForCurrentPayrollsAsync(List<Guid> payrollIds)
        {
            var absences = await absencesRepository.GetAbsencesForCurrentPayrollsAsync<Absence>(payrollIds);
            var hospitals = await absencesRepository.GetAbsencesForCurrentPayrollsAsync<Hospital>(payrollIds);

            var absenceDtos = absences.Select(mapper.Map<AbsenceHospitalDTO>);

            var hospitalDtos = hospitals.Select(mapper.Map<AbsenceHospitalDTO>);

            var result = absenceDtos.Concat(hospitalDtos).ToList();

            return result;
        }

        public async Task<AbsenceHospitalDTO> GetAbsenceByIdAsync(Guid absenceId, bool isHospital)
        {
            return isHospital
                ? mapper.Map<AbsenceHospitalDTO>(await absencesRepository.GetByIdAsync<Hospital>(absenceId))
                : mapper.Map<AbsenceHospitalDTO>(await absencesRepository.GetByIdAsync<Absence>(absenceId));
        }
    }
}