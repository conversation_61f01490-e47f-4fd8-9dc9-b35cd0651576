using AutoMapper;
using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Notifications.Hospitals;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Payrolls;

namespace WorkTimeApi.Handlers.Absences
{
    public class AddAbsenceHandler(IAbsencesService absencesService,
        IPayrollsService payrollsService,
        IEmployeesService employeesService,
        IMapper mapper,
        IMediator mediator,
        GlobalUser user) : IRequestHandler<AddAbsenceRequest, IResult>
    {
        public async Task<IResult> Handle(AddAbsenceRequest request, CancellationToken cancellationToken)
        {
            var absences = new List<EventDTO>();

            foreach (var payrollId in request.PayrollIds)
            {
                var absence = mapper.Map<EventDTO>(request);
                absence.WorkTimePayrollId = payrollId;
                absences.Add(absence);
            }

            var addAbsenceResult = await absencesService.AddAbsencesAsync(absences);
            if (addAbsenceResult.IsError)
                return Results.BadRequest(addAbsenceResult.ValidationError);

            var absenceTypeHospital = absences.FirstOrDefault().EventType.IsHospital();
            var payroll = await payrollsService.GetByIdAsync(request.PayrollIds.FirstOrDefault());
            if (payroll is null)
                return Results.BadRequest("Invalid payroll ID.");

            var employee = await employeesService.GetEmployeeAsync(payroll.EmployeeGuid);
            var companyId = payroll.CompanyId;

            
            if (absenceTypeHospital)
            {
                var hospitalNotification = new HospitalsAddedNotification([.. addAbsenceResult.Value], companyId, user.Id, $"{employee.FirstName} {employee.LastName}");
                mediator.Publish(hospitalNotification, cancellationToken);
            }
            else
            {
                var notification = new AbsencesAddedNotification([.. addAbsenceResult.Value], companyId, user.Id, $"{employee.FirstName} {employee.LastName}");
                mediator.Publish(notification, cancellationToken);
            }

            return Results.Ok(addAbsenceResult);
        }
    }
}
