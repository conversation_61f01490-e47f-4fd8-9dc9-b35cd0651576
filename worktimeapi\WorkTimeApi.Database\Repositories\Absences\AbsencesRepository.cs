﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Absences;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.Database.Repositories.Absences
{
    public class AbsencesRepository(IDbContextFactory<WorkTimeApiDbContext> contextFactory, ICalendarService calendarService) : IAbsencesRepository
    {
        public async Task<List<T>> AddAbsencesAsync<T>(List<T> leaves) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            await dbSet.AddRangeAsync(leaves);
            await context.SaveChangesAsync();
            return leaves;
        }

        public async Task<T> AddAbsenceAsync<T>(T absence) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();
            await dbSet.AddAsync(absence);
            await context.SaveChangesAsync();
            return absence;
        }

        public async Task<T> DeletePersonalAbsenceAsync<T>(Guid absenceId, Guid payrollId, Guid userId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            var absence = await dbSet.Include(e => e.Payroll).ThenInclude(p => p.Employee).FirstOrDefaultAsync(e => e.Id == absenceId);
            if (absence is null || absence.PayrollId != payrollId || absence.Payroll.Employee.UserId != userId)
                throw new Exception("Няма открит ваш отпуск с това Id");

            absence.Status = AbsenceStatus.Deleted;

            await context.SaveChangesAsync();

            return absence;
        }

        public async Task<T> DeleteAbsenceAsync<T>(Guid absenceId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();
            var absence = await dbSet.FindAsync(absenceId) ?? throw new Exception("Отпуска с това Id не беше открита!");

            absence.Status = AbsenceStatus.DeletedByAdmin;
            await context.SaveChangesAsync();

            return absence;
        }

        public async Task<T?> GetByIdAsync<T>(Guid absenceId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            if (typeof(T) == typeof(Absence))
            {
                return await context.Absences
                   .Include(a => a.Payroll)
                   .ThenInclude(p => p.Employee)
                   .Include(a => a.Approvals)
                   .FirstOrDefaultAsync(h => h.Id == absenceId) as T;
            }
            else if (typeof(T) == typeof(Hospital))
            {
                return await context.Hospitals
                   .Include(a => a.Payroll)
                   .ThenInclude(p => p.Employee)
                   .Include(a => a.Approvals)
                   .FirstOrDefaultAsync(h => h.Id == absenceId) as T;
            }

            return await dbSet
               .Include(a => a.Payroll)
               .ThenInclude(p => p.Employee)
               .FirstOrDefaultAsync(h => h.Id == absenceId);
        }

        public async Task<IEnumerable<T>> GetAbsencesForMonthAsync<T>(Guid companyId, DateTime date) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            var startOfMonth = new DateTime(date.Year, date.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            return await dbSet
             .Include(a => a.Payroll)
             .Where(a => a.FromDate <= endOfMonth
                      && a.ToDate >= startOfMonth
                      && a.Payroll.CompanyId == companyId
                      && a.Status != AbsenceStatus.Deleted && a.Status != AbsenceStatus.Declined && a.Status != AbsenceStatus.DeletedByAdmin)
             .ToListAsync();
        }

        public async Task<IEnumerable<T>> GetAbsencesForCurrentPayrollsAsync<T>(List<Guid> payrollIds) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            return await dbSet
             .Include(a => a.Payroll)
             .Where(a => payrollIds.Contains(a.Payroll.Id)).ToListAsync();
        }

        public async Task<IEnumerable<T>> GetEmployeeAbsencesAsync<T>(Guid employeeId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            return await dbSet
             .Include(a => a.Payroll)
             .Where(a => a.Payroll.EmployeeId == employeeId
                      && a.Status != AbsenceStatus.Deleted && a.Status != AbsenceStatus.Declined && a.Status != AbsenceStatus.DeletedByAdmin)
             .ToListAsync();
        }

        public async Task<IEnumerable<T>> GetPayrollAbsenceAsync<T>(Guid payrollId, Expression<Func<T, bool>> predicate) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            return await dbSet
             .Include(a => a.Payroll)
             .Where(a => a.Payroll.Id == payrollId
                      && a.Status != AbsenceStatus.Deleted && a.Status != AbsenceStatus.Declined && a.Status != AbsenceStatus.DeletedByAdmin)
             .Where(predicate)
             .ToListAsync();
        }

        public async Task<IEnumerable<T>> GetCompanyAbsencesForTRZAsync<T>(Guid companyId, DateTime month, bool includeExported = false) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            var startOfMonth = new DateTime(month.Year, month.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            var query = dbSet
                .Include(a => a.Payroll)
                .Where(a => a.FromDate <= endOfMonth
                         && a.FromDate >= startOfMonth
                         && a.Payroll.CompanyId == companyId
                         && a.Status != AbsenceStatus.Pending
                         && a.Status != AbsenceStatus.EditedByEmployee
                         && a.Status != AbsenceStatus.Declined
                         && a.ExportStatus != AbsenceExportStatus.TRZExported);

            if (!includeExported)
            {
                query = query.Where(a => a.ExportStatus != AbsenceExportStatus.Exported);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<T>> MarkExportedAsync<T>(Guid companyId, IEnumerable<Guid> ids) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            var entities = await dbSet
                .Include(a => a.Payroll)
                .ThenInclude(p => p.Employee).Where(a => ids.Contains(a.Id) && a.Payroll.Employee.CompanyId == companyId)
                .ToListAsync();

            foreach (var entity in entities)
            {
                entity.ExportStatus = AbsenceExportStatus.Exported;
            }

            await context.SaveChangesAsync();
            return entities;
        }

        public async Task<T> UpdateAbsenceAsync<T>(T absence) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();

            context.Entry(absence).State = EntityState.Modified;
            await context.SaveChangesAsync();
            return absence;
        }

        public async Task<T?> GetByIdForUpdateAsync<T>(Guid absenceId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            if (typeof(T) == typeof(Absence))
            {
                return await context.Absences
                   .FirstOrDefaultAsync(h => h.Id == absenceId) as T;
            }
            else if (typeof(T) == typeof(Hospital))
            {
                return await context.Hospitals
                   .FirstOrDefaultAsync(h => h.Id == absenceId) as T;
            }

            return await dbSet
               .FirstOrDefaultAsync(h => h.Id == absenceId);
        }

        public async Task<Employee?> GetEmployeeByAbsenceIdAsync<T>(Guid absenceId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();

            if (typeof(T) == typeof(Absence))
            {
                return await context.Absences
                    .Where(a => a.Id == absenceId)
                    .Select(a => a.Payroll.Employee)
                    .FirstOrDefaultAsync();
            }
            else if (typeof(T) == typeof(Hospital))
            {
                return await context.Hospitals
                    .Where(h => h.Id == absenceId)
                    .Select(h => h.Payroll.Employee)
                    .FirstOrDefaultAsync();
            }

            return null;
        }

        public async Task<List<T>> UpdateOverlapingAbsences<T>(Guid hospitalId) where T : BaseAbsence
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var dbSet = context.Set<T>();

            var hospital = await GetByIdAsync<Hospital>(hospitalId);
            if (hospital is null)
                return [];

            var payroll = await context.Payrolls.FirstOrDefaultAsync(p => p.Id == hospital.PayrollId);
            var employee = await context.Employees.Include(e => e.Payrolls).FirstOrDefaultAsync(e => e.Id == payroll.EmployeeId);
            var payrollids = employee.Payrolls.Select(p => p.Id);
            var newlyAddedAbsences = new List<Absence>();
            var overlapingAbsences = context.Absences.Where(a =>
            payrollids.Contains(a.PayrollId) && a.FromDate <= hospital.ToDate &&
            a.ToDate >= hospital.FromDate).ToList();

            foreach (var overlapingAbsence in overlapingAbsences)
            {
                if (overlapingAbsence.FromDate >= hospital.FromDate && overlapingAbsence.ToDate <= hospital.ToDate)
                {
                    overlapingAbsence.Status = AbsenceStatus.DeletedByAdmin;
                    context.Absences.Remove(overlapingAbsence);
                    continue;
                }
                else if (hospital.FromDate <= overlapingAbsence.FromDate && hospital.ToDate < overlapingAbsence.ToDate)
                {
                    overlapingAbsence.FromDate = hospital.ToDate.AddDays(1);
                }
                else if (hospital.FromDate > overlapingAbsence.FromDate && hospital.ToDate >= overlapingAbsence.ToDate)
                {
                    overlapingAbsence.ToDate = hospital.FromDate.AddDays(-1);
                }
                else if (hospital.FromDate > overlapingAbsence.FromDate && hospital.ToDate < overlapingAbsence.ToDate)
                {
                    var newAbsence = new Absence
                    {
                        FromDate = hospital.ToDate.AddDays(1),
                        ToDate = overlapingAbsence.ToDate,
                        AbsenceType = overlapingAbsence.AbsenceType,
                        Duration = await calendarService.CalculateEventDuration(overlapingAbsence.ToDate.AddDays(1), overlapingAbsence.ToDate),
                        PayrollId = overlapingAbsence.PayrollId,
                        Reference = overlapingAbsence.Reference,
                        Status = overlapingAbsence.Status
                    };

                    overlapingAbsence.ToDate = hospital.FromDate.AddDays(-1);

                    context.Absences.Add(newAbsence);
                    newlyAddedAbsences.Add(newAbsence);
                }

                overlapingAbsence.Duration = await calendarService.CalculateEventDuration(overlapingAbsence.FromDate, overlapingAbsence.ToDate);
                context.Entry(overlapingAbsence).State = EntityState.Modified;
            }

            await context.SaveChangesAsync();

            return overlapingAbsences.Concat(newlyAddedAbsences).Cast<T>().ToList();
        }
    }
}
