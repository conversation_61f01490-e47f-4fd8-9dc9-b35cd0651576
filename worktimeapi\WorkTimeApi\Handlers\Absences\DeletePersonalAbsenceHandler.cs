﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Absences
{
    public class DeletePersonalAbsenceHandler(IAbsencesService absencesService, IEmployeesService employeesService,
        IMediator mediator, GlobalUser globalUser) : IRequestHandler<DeletePersonalAbsenceRequest, IResult>
    {
        public async Task<IResult> Handle(DeletePersonalAbsenceRequest request, CancellationToken cancellationToken)
        {
            var absence = await absencesService.GetAbsenceByIdAsync(request.AbsenceId, request.IsHospital);
            var employee = await employeesService.GetPayrollEmployeeAsync(request.PayrollId);
            await absencesService.DeletePersonalAbsenceAsync(request.AbsenceId, request.PayrollId, request.IsHospital);
            if (absence.Status != Common.Enums.AbsenceStatus.Pending)
            {
                var notification = new AbsenceDeleteRequestByEmployeeNotification(absence, request.CompanyId, globalUser.Id, $"{employee.FirstName} {employee.LastName}");
                mediator.Publish(notification, cancellationToken);
            }

            return Results.Ok(absence);
        }
    }
}