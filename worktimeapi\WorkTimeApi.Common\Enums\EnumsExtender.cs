﻿using System.ComponentModel;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.TRZ;

namespace WorkTimeApi.Common.Enums
{
    public static class EnumsExtender
    {
        public static TypeOfAppointment ToTypeOfAppointment(this NomenclatureDTO? dto)
        {
            return dto != null && int.TryParse(dto.Identifier, out var id)
                ? (TypeOfAppointment)id
                : TypeOfAppointment.EmploymentContracts;
        }

        public static AddressPurpose ToAddressPurpose(this TRZAddressPurposeDTO? dto)
        {
            return dto != null
                ? (AddressPurpose)dto.Identifier
                : AddressPurpose.ForContact;
        }

        public static EGNTypes ToENGType(this NomenclatureDTO? dto)
        {
            return dto != null && int.TryParse(dto.Identifier, out var id)
                ? (EGNTypes)id
                : EGNTypes.ЕГН;
        }

        public static ContractReason ToContractReason(this NomenclatureDTO? dto)
        {
            return dto != null && int.TryParse(dto.Identifier, out var id)
                ? (ContractReason)id
                : ContractReason.ContractReason_Трудов_договор_чл68_ал1_т2;
        }

        public static NomenclatureDTO ToNomenclatureDTO<TEnum>(this TEnum enumValue)
                where TEnum : struct, Enum
        {
            return new NomenclatureDTO
            {
                Identifier = Convert.ToInt32(enumValue).ToString()
            };
        }
        public static TRZAddressPurposeDTO ToTRZAddressPurposeDTO<TEnum>(this TEnum enumValue)
        where TEnum : struct, Enum
        {
            return new TRZAddressPurposeDTO
            {
                Identifier = Convert.ToInt32(enumValue)
            };
        }

        public static string GetDescription(this EventType eventType)
        {
            var fieldInfo = eventType.GetType().GetField(eventType.ToString());
            var attributes = (DescriptionAttribute[])fieldInfo?.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes?.Length > 0 ? attributes[0].Description : eventType.ToString();
        }

        public static EnumValueDTO ToEnumValueDTO<TEnum>(this TEnum enumValue)
        where TEnum : struct, Enum
        {
            return new EnumValueDTO
            {
                Identifier = Convert.ToInt32(enumValue)
            };
        }

        public static BankAccountPurpose ToBankAccountPurpose(this NomenclatureDTO? dto)
        {
            return dto != null && int.TryParse(dto.Identifier, out var id)
                ? (BankAccountPurpose)id
                : BankAccountPurpose.Salary;
        }

        public static Country ToCountry(this EnumValueDTO? dto)
        {
            return dto != null
                ? (Country)dto.Identifier
                : Country.BG;
        }

        public static BankAccountPurpose ToBankAccountPurpose(this EnumValueDTO? dto)
        {
            return dto != null
                ? (BankAccountPurpose)dto.Identifier
                : BankAccountPurpose.Salary;
        }

        public static bool IsHospital(this EventType type)
        {
            return type switch
            {
                EventType.Болничен or
                EventType.ГледанеНаБоленЧленОтСемейството or
                EventType.ТрудоваЗлополука or
                EventType.ПрофесионалнаБолест or
                EventType.НеплатенЗаВременнаНеработоспособност or
                EventType.БолниченПоБременност or
                EventType.БолниченСледРаждане or
                EventType.НеплатенЗаБременностИРаждане or

                EventType.ОтпускЗаМайкаСледРаждане135До410Ден or
                EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца or
                EventType.ОтпускДо15ДниПриРажданеНаДете or
                EventType.ОтпускЗаОтглежданеНаДетеДо2Години or
                EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст or
                EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст or
                EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст or
                EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата
                    => true,

                _ => false
            };
        }

        public static bool IsHospital(this TRZEventType type) => type switch
        {
            TRZEventType.Болничен or
            TRZEventType.ГледанеНаБоленЧлен or
            TRZEventType.ТрудоваЗлополука or
            TRZEventType.ПрофесионалнаБолест or
            TRZEventType.НеплатенПоНетрудоспособност or
            TRZEventType.БолниченПоБременност or
            TRZEventType.БолниченСледРаждане or
            TRZEventType.НеплатенЗаБременност or
            TRZEventType.ОтпускМайка135До410 or
            TRZEventType.ОтпускБащаНад6Месеца or
            TRZEventType.Отпуск15ДниРаждане or
            TRZEventType.ОтпускЗаДетеДо2 or
            TRZEventType.ОтпускОсиновяванеДо5 or
            TRZEventType.НеплатенОсиновяванеДо5 or
            TRZEventType.БащаОсиновителДо5 or
            TRZEventType.БащаГледаДетеДо8
                => true,

            _ => false
        };

        public static bool IsCivilContract(this TypeOfAppointment typeOfAppointment)
        {
            return typeOfAppointment switch
            {
                TypeOfAppointment.CivilContractDividends or
                TypeOfAppointment.CivilContractOther or
                TypeOfAppointment.CivilContractStandart or
                TypeOfAppointment.CivilContractLandlords
                    => true,

                _ => false
            };
        }

        public static PayrollCategories GetAppointmentCategory(this TypeOfAppointment typeOfAppointment)
        {
            return typeOfAppointment switch
            {
                TypeOfAppointment.EmploymentRelationship or
                TypeOfAppointment.EmploymentContracts or
                TypeOfAppointment.ManagementContracts or
                TypeOfAppointment.SelfEmployedOwner
                    => PayrollCategories.Employees,
                TypeOfAppointment.Candidates or
                TypeOfAppointment.PassedInterviewMeeting or
                TypeOfAppointment.InvitedToWork or
                TypeOfAppointment.ToConcludeContract
                    => PayrollCategories.ApplicantsForAppointment,
                TypeOfAppointment.RepresentativeOfAnExternalCompany or
                TypeOfAppointment.CivilContractStandart or
                TypeOfAppointment.CivilContractLandlords or
                TypeOfAppointment.CivilContractDividends or
                TypeOfAppointment.CivilContractOther
                   => PayrollCategories.ApplicantsForAppointment,
                _ => throw new NotImplementedException()
            };
        }

        public static EventType ToEventType(this TRZEventType trzEventType)
        {
            return trzEventType switch
            {
                TRZEventType.ПлатенГодишенОтпуск => EventType.ПлатенГодишенОтпуск,
                TRZEventType.ПлатенГодишенОтпускЗаМиналаГодина => EventType.ПлатенГодишенОтпускЗаМиналаГодина,
                TRZEventType.НеплатенСОсигурителенСтажОтОсигурител => EventType.НеплатенСОсигурителенСтажОтОсигурител,
                TRZEventType.НеплатенСОсигурителенСтажОтОсигурен => EventType.НеплатенСОсигурителенСтажОтОсигурен,
                TRZEventType.НеплатенЗаДетеДо8 => EventType.НеплатенЗаДетеДо8,
                TRZEventType.НеплатенБезСтажОтОсигурител => EventType.НеплатенБезСтажОтОсигурител,
                TRZEventType.НеплатенБезСтажОтОсигурен => EventType.НеплатенБезСтажОтОсигурен,
                TRZEventType.Самоотлъчване => EventType.Самоотлъчване,
                TRZEventType.Болничен => EventType.Болничен,
                TRZEventType.ГледанеНаБоленЧлен => EventType.ГледанеНаБоленЧленОтСемейството,
                TRZEventType.ТрудоваЗлополука => EventType.ТрудоваЗлополука,
                TRZEventType.ПрофесионалнаБолест => EventType.ПрофесионалнаБолест,
                TRZEventType.НеплатенПоНетрудоспособност => EventType.НеплатенЗаВременнаНеработоспособност,
                TRZEventType.БолниченПоБременност => EventType.БолниченПоБременност,
                TRZEventType.БолниченСледРаждане => EventType.БолниченСледРаждане,
                TRZEventType.НеплатенЗаБременност => EventType.НеплатенЗаБременностИРаждане,
                TRZEventType.ОтпускМайка135До410 => EventType.ОтпускЗаМайкаСледРаждане135До410Ден,
                TRZEventType.ОтпускБащаНад6Месеца => EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца,
                TRZEventType.Отпуск15ДниРаждане => EventType.ОтпускДо15ДниПриРажданеНаДете,
                TRZEventType.ОтпускЗаДетеДо2 => EventType.ОтпускЗаОтглежданеНаДетеДо2Години,
                TRZEventType.ПлатенПоДругиЧленове => EventType.ПлатенПоДругиЧленове,
                TRZEventType.ОтпускОсиновяванеДо5 => EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
                TRZEventType.НеплатенОсиновяванеДо5 => EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
                TRZEventType.БащаОсиновителДо5 => EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
                TRZEventType.ПлатенПоЧл173а => EventType.ПлатенПоЧл173а,
                TRZEventType.БащаГледаДетеДо8 => EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата,
                _ => EventType.ПлатенГодишенОтпуск
            };
        }

        public static TRZEventType ToTRZEventType(this EventType eventType)
        {
            return eventType switch
            {
                EventType.ПлатенГодишенОтпуск => TRZEventType.ПлатенГодишенОтпуск,
                EventType.ПлатенГодишенОтпускЗаМиналаГодина => TRZEventType.ПлатенГодишенОтпускЗаМиналаГодина,
                EventType.НеплатенСОсигурителенСтажОтОсигурител => TRZEventType.НеплатенСОсигурителенСтажОтОсигурител,
                EventType.НеплатенСОсигурителенСтажОтОсигурен => TRZEventType.НеплатенСОсигурителенСтажОтОсигурен,
                EventType.НеплатенЗаДетеДо8 => TRZEventType.НеплатенЗаДетеДо8,
                EventType.НеплатенБезСтажОтОсигурител => TRZEventType.НеплатенБезСтажОтОсигурител,
                EventType.НеплатенБезСтажОтОсигурен => TRZEventType.НеплатенБезСтажОтОсигурен,
                EventType.Самоотлъчване => TRZEventType.Самоотлъчване,
                EventType.Болничен => TRZEventType.Болничен,
                EventType.ГледанеНаБоленЧленОтСемейството => TRZEventType.ГледанеНаБоленЧлен,
                EventType.ТрудоваЗлополука => TRZEventType.ТрудоваЗлополука,
                EventType.ПрофесионалнаБолест => TRZEventType.ПрофесионалнаБолест,
                EventType.НеплатенЗаВременнаНеработоспособност => TRZEventType.НеплатенПоНетрудоспособност,
                EventType.БолниченПоБременност => TRZEventType.БолниченПоБременност,
                EventType.БолниченСледРаждане => TRZEventType.БолниченСледРаждане,
                EventType.НеплатенЗаБременностИРаждане => TRZEventType.НеплатенЗаБременност,
                EventType.ОтпускЗаМайкаСледРаждане135До410Ден => TRZEventType.ОтпускМайка135До410,
                EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца => TRZEventType.ОтпускБащаНад6Месеца,
                EventType.ОтпускДо15ДниПриРажданеНаДете => TRZEventType.Отпуск15ДниРаждане,
                EventType.ОтпускЗаОтглежданеНаДетеДо2Години => TRZEventType.ОтпускЗаДетеДо2,
                EventType.ПлатенПоДругиЧленове => TRZEventType.ПлатенПоДругиЧленове,
                EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст => TRZEventType.ОтпускОсиновяванеДо5,
                EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст => TRZEventType.НеплатенОсиновяванеДо5,
                EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст => TRZEventType.БащаОсиновителДо5,
                EventType.ПлатенПоЧл173а => TRZEventType.ПлатенПоЧл173а,
                EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата => TRZEventType.БащаГледаДетеДо8,
                EventType.НеплатенПоНетрудоспособност => TRZEventType.НеплатенПоНетрудоспособност,

                EventType.ПлатенОбучениеЧл169Ал1 or
                EventType.ПлатенДържавенИзпитЧл169Ал3 or
                EventType.ПлатенНаучнаСтепенЧл169Ал4 or
                EventType.ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1 or
                EventType.ПлатенКандидатстванеУниверситетЧл170Ал1Т2 or
                EventType.ПлатенСлужебенТворческиЧл161Ал1 => TRZEventType.ПлатенПоДругиЧленове,

                EventType.ИнцидентенГражданскиБракЧл157Ал1Т1 or
                EventType.ИнцидентенКръводаряванеЧл157Ал1Т2 or
                EventType.ИнцидентенСмъртРоднинаЧл157Ал1Т3 or
                EventType.ИнцидентенЯвяванеВСъдаЧл157Ал1Т4 or
                EventType.ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5 or
                EventType.ИнцидентенДоброволецПриБедствияЧл157Ал1Т7 or
                EventType.ИнцидентенПрегледБременностИнВитроЧл157Ал2 or
                EventType.ИнцидентенСлужбаВРезервЧл158Ал1 or
                EventType.ИнцидентенДруг => TRZEventType.ПлатенПоДругиЧленове,

                EventType.НеплатенКандидатстванеСредноУчилищеЧл170Ал2 or
                EventType.НеплатенКандидатстванеУниверситетЧл170Ал2 or
                EventType.НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1 or
                EventType.НеплатенДържавенИзпитДиплСУЧл171Ал1Т2 or
                EventType.НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3 or
                EventType.НеплатенДисертацияЧл171Ал1Т4 or
                EventType.НеплатенСлужебенТворческиЧл161Ал1 => TRZEventType.НеплатенСОсигурителенСтажОтОсигурен,

                EventType.ПлатенКомпенсация => TRZEventType.ПлатенПоДругиЧленове,

                _ => TRZEventType.ПлатенГодишенОтпуск
            };
        }
    }
}
