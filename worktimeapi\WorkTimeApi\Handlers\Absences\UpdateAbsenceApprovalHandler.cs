﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Absences
{
    public class UpdateAbsenceApprovalHandler(IAbsencesService absencesService,
        IMediator mediator, GlobalUser globalUser) : IRequestHandler<UpdateAbsenceApprovalRequest, IResult>
    {
        public async Task<IResult> Handle(UpdateAbsenceApprovalRequest request, CancellationToken cancellationToken)
        {
            var (oldAbsence, newAbsence) = await absencesService.UpdateAbsenceApprovalRequest(request.AbsenceId, request.Status, request.IsHospital, request.Message);

            var employeeDTO = await absencesService.GetEmployeeDTOByAbsenceIdAsync(request.AbsenceId, request.IsHospital);

            var notificationConfirm = new AbsenceStatusChangedNotification(newAbsence, employeeDTO.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}", oldAbsence);
            mediator.Publish(notificationConfirm, cancellationToken);

            var updatedAbsences = await absencesService.UpdateOverlapingAbsences(request.AbsenceId);

            if (updatedAbsences.Any())
            {
                var notificationUpdatedAbsences = new UpdateAbsencesNotification(updatedAbsences, employeeDTO.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}", employeeDTO.WorkTimeId);
                mediator.Publish(notificationUpdatedAbsences, cancellationToken);
            }

            return Results.Ok(newAbsence);
        }
    }
}
