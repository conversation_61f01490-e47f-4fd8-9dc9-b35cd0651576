﻿using Gateway.Connections.Interfaces;
using Gateway.Extenstions.HttpClientExtensions;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Notifications;
using WorkTimeApi.Common.Requests.OnboardingDocuments;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Common.Requests.Roles;
using WorkTimeApi.Common.Requests.StructureLevels;
using WorkTimeApi.Common.Requests.Templates;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Common.Requests.Users;

namespace Gateway.Connections
{
    public class WorkTimeApiConnection : IWorkTimeApiConnection
    {
        private readonly HttpClient _httpClient;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public WorkTimeApiConnection(IConfiguration configuration, HttpClient httpClient, IHttpContextAccessor httpContextAccessor)
        {
            _httpClient = httpClient;
            _httpContextAccessor = httpContextAccessor;
            _httpClient.BaseAddress = new Uri(configuration["WorkTimeApiUrl"]!);
        }

        public Task<HttpResponseMessage> AddPayrollAsync(AddPayrollRequest basicPayrollRequest)
        {
            return _httpClient.PostAsJsonAsync("payrolls/add-payroll", basicPayrollRequest);
        }

        public Task<HttpResponseMessage> DeletePayrollAsync(DeletePayrollRequest deletePayrollRequest)
        {
            return _httpClient.DeleteAsync($"payrolls/delete-payroll?payrollid={deletePayrollRequest.PayrollId}");
        }

        public Task<HttpResponseMessage> AddTemplateAsync(AddTemplateRequest addTemplateRequest)
        {
            return _httpClient.PostAsJsonAsync("templates/add-template", addTemplateRequest);
        }

        public Task<HttpResponseMessage> LoadTemplatesAsync(LoadTemplatesRequest loadTemplateRequest)
        {
            return _httpClient.GetAsync($"templates/load-template?companyId={loadTemplateRequest.CompanyId}");
        }

        public Task<HttpResponseMessage> AddOnboardingDocumentAsync(AddOnboardingDocumentRequest addOnboardingDocumentRequest)
        {
            return _httpClient.PostAsJsonAsync("onboarding-documents/add-document", addOnboardingDocumentRequest);
        }

        public Task<HttpResponseMessage> GetCompaniesAsync(GetCompaniesRequest getCompaniesRequest)
        {
            return _httpClient.GetAsync($"companies?userId={getCompaniesRequest.UserId}");
        }

        public Task<HttpResponseMessage> CreateCompanyAsync(CreateCompanyRequest createCompanyRequest)
        {
            if (_httpContextAccessor.HttpContext is null)
                throw new Exception("HttpContext is null. Ensure that IHttpContextAccessor is properly configured in your application.");

            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            _httpClient.DefaultRequestHeaders.Remove("refresh-token");
            var auth = _httpContextAccessor.HttpContext.Request.Headers.Authorization.FirstOrDefault();
            var refresh = _httpContextAccessor.HttpContext.Request.Headers["Refresh-Token"].FirstOrDefault();
            if (!string.IsNullOrEmpty(auth))
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", auth);
            if (!string.IsNullOrEmpty(refresh))
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("refresh-token", refresh);

            return _httpClient.PostAsJsonAsync("companies", createCompanyRequest);
        }

        public Task<HttpResponseMessage> AddStructureLevelAsync(AddStructureLevelRequest addStructureLevelRequest)
        {
            return _httpClient.PostAsJsonAsync("structure-levels", addStructureLevelRequest);
        }

        public Task<HttpResponseMessage> GetStructureLevelsAsync(Guid companyId)
        {
            return _httpClient.GetAsync($"structure-levels?companyId={companyId}");
        }

        public Task<HttpResponseMessage> DeleteStructureLevelAsync(Guid structureLevelId)
        {
            return _httpClient.DeleteAsync($"structure-levels/{structureLevelId}");
        }

        public Task<HttpResponseMessage> AddEmployeeAsync(AddEmployeeRequest addEmployeeRequest)
        {
            return _httpClient.PostAsJsonAsync("employees", addEmployeeRequest);
        }

        public Task<HttpResponseMessage> AddUserWithEmployeeAsync(AddUserWithEmployeeRequest addUserWithEmployeeRequest)
        {
            return _httpClient.PostAsJsonAsync("user-employee", addUserWithEmployeeRequest);
        }

        public Task<HttpResponseMessage> AddUserAsync(AddUserRequest addUserRequest)
        {
            return _httpClient.PostAsJsonAsync("user", addUserRequest);
        }

        public Task<HttpResponseMessage> CreateEmployeeForCompanyAsync(CreateEmployeeForCompanyRequest createEmployeeRequest)
        {
            return _httpClient.PostAsJsonAsync($"companies/{createEmployeeRequest.CompanyId}/employees", createEmployeeRequest);
        }

        public Task<HttpResponseMessage> AddEmployeeCompanyAsync(AddEmployeeCompanyRequest addEmployeeCompanyRequest)
        {
            return _httpClient.PostAsJsonAsync("employee-company", addEmployeeCompanyRequest);
        }

        public Task<HttpResponseMessage> GetCompanyByIdAsync(Guid companyId)
        {
            return _httpClient.GetAsync($"company?companyId={companyId}");
        }

        public Task<HttpResponseMessage> UpdateUserHasSignedInAsync(UpdateUserHasSignedInRequest updateUserHasSignedInRequest)
        {
            return _httpClient.PostAsJsonAsync($"users/update-has-signed-in", updateUserHasSignedInRequest);
        }

        public Task<HttpResponseMessage> LoadEmployeesAsync(LoadEmployeesRequest loadEmployeesRequest)
        {
            return _httpClient.GetAsync($"employees?companyid={loadEmployeesRequest.CompanyId}");
        }

        public Task<HttpResponseMessage> LoadUserEmployeeAsync(LoadUserEmployeeRequest loadUserEmployeeRequest)
        {
            return _httpClient.GetAsync($"user-employee/{loadUserEmployeeRequest.UserId}/{loadUserEmployeeRequest.CompanyId}");
        }

        public Task<HttpResponseMessage> GetUserAsync(GetUserRequest getUserRequest)
        {
            return _httpClient.GetAsync($"users/{getUserRequest.Id}");
        }

        public Task<HttpResponseMessage> LoadEmployeePayrollListAsync(LoadEmployeePayrollRequest loadEmployeePayrollRequest)
        {
            return _httpClient.GetAuthAsync($"employee-payrolls?companyId={loadEmployeePayrollRequest.CompanyId}", _httpContextAccessor);
        }

        public Task<HttpResponseMessage> LoadPendingEmployeePayrollListAsync(LoadPendingEmployeePayrollRequest loadPeningEmployeePayrollRequest)
        {
            return _httpClient.GetAsync($"pending-employee-payrolls?companyId={loadPeningEmployeePayrollRequest.CompanyId}");
        }

        public Task<HttpResponseMessage> GetContractsAsync()
        {
            return _httpClient.GetAsync($"contracts");
        }

        public Task<HttpResponseMessage> GetTerminationReasonAsync()
        {
            return _httpClient.GetAsync($"termination-reason");
        }
        public Task<HttpResponseMessage> GetCountriesAsync()
        {
            return _httpClient.GetAsync($"countries");
        }

        public Task<HttpResponseMessage> GetIncomeTypesAsync()
        {
            return _httpClient.GetAsync($"income-types");
        }

        public Task<HttpResponseMessage> GetTZPBsAsync()
        {
            return _httpClient.GetAsync($"tzpbs");
        }

        public Task<HttpResponseMessage> GetNKPDsAsync()
        {
            return _httpClient.GetAsync($"nkpds");
        }

        public Task<HttpResponseMessage> GetQualificationGroupsAsync()
        {
            return _httpClient.GetAsync($"qualification-groups");
        }

        public Task<HttpResponseMessage> ImportCompanyAsync(ImportTRZCompanyRequest importCompanyRequest)
        {
            return _httpClient.PostAsJsonAsync("trz/companies", importCompanyRequest);
        }

        public Task<HttpResponseMessage> GetDistrictsAsync()
        {
            return _httpClient.GetAsync($"default-districts-data");
        }

        public Task<HttpResponseMessage> GetMunicipalitiesAsync()
        {
            return _httpClient.GetAsync($"default-municipalities-data");
        }

        public Task<HttpResponseMessage> GetCitiesAsync()
        {
            return _httpClient.GetAsync($"default-cities-data");
        }

        public Task<HttpResponseMessage> AddTRZEmployeesAsync(AddTRZEmployeesRequest request)
        {
            return _httpClient.PostAsJsonAsync($"trz/employees", request);
        }

        public Task<HttpResponseMessage> UpdateTRZDepartmentsAsync(UpdateTRZDepartmentsRequest request)
        {
            return _httpClient.PostAsJsonAsync($"trz/departments", request);
        }

        public Task<HttpResponseMessage> GetMODsAsync()
        {
            return _httpClient.GetAsync($"mods");
        }

        public Task<HttpResponseMessage> GetKidsAsync()
        {
            return _httpClient.GetAsync($"kids");
        }

        public Task<HttpResponseMessage> AddTRZPendingEventsAsync(AddTRZPendingEventsRequest addTRZPendingEventsRequest)
        {
            return _httpClient.PostAsJsonAsync($"trz/pending-events", addTRZPendingEventsRequest);
        }

        public Task<HttpResponseMessage> AddTRZEventsAsync(AddTRZEventsRequest addTRZAbsencesRequest)
        {
            return _httpClient.PostAsJsonAsync($"trz/events", addTRZAbsencesRequest);
        }

        public Task<HttpResponseMessage> GetTRZEventsAsync(GetTRZEventsRequest getTRZEventsRequest)
        {
            return _httpClient.GetAsync($"trz/events?payrollId={getTRZEventsRequest.PayrollId}");
        }

        public Task<HttpResponseMessage> ImportTRZEventAsync(ImportTRZEventRequest importTRZEventRequest)
        {
            return _httpClient.PostAsJsonAsync($"trz/event", importTRZEventRequest);
        }

        public Task<HttpResponseMessage> GetAbsenceTypesAsync()
        {
            return _httpClient.GetAsync($"absence-types");
        }

        public Task<HttpResponseMessage> GetHospitalTypesAsync()
        {
            return _httpClient.GetAsync($"hospital-types");
        }

        public Task<HttpResponseMessage> GetTRZEmployeesAsync(GetTRZEmployeesRequest getTRZEmployeesRequest)
        {
            return _httpClient.GetAsync($"trz/employees?companyId={getTRZEmployeesRequest.CompanyId}");
        }

        public Task<HttpResponseMessage> ImportPendingEmployeesAsync(ImportPendingEmployeesRequest importPendingEmployeeRequest)
        {
            return _httpClient.PostAsJsonAuthAsync($"pending-employee-payrolls/import", importPendingEmployeeRequest, _httpContextAccessor);
        }

        public Task<HttpResponseMessage> GetAbsencesAndHospitalsAsync(AbsencesAndHospitalsRequest absencesAndHospitalsRequest)
        {
            return _httpClient.GetAsync($"absences/monthly?companyId={absencesAndHospitalsRequest.CompanyId}&date={absencesAndHospitalsRequest.Date.ToString("yyyy-MM-dd")}");
        }

        public Task<HttpResponseMessage> UpdateCompanyInvitationAsync(CompanyInvitationRequest companyInvitationRequest)
        {
            return _httpClient.PutAsJsonAsync("company/invitation", companyInvitationRequest);
        }

        public Task<HttpResponseMessage> GetCompanyByEmailAndBulstatAsync(string email, string bulstat)
        {
            return _httpClient.GetAsync($"company/{email}/{bulstat}");
        }

        public Task<HttpResponseMessage> EditCompanyAsync(EditCompanyRequest editedCompanyResponse)
        {
            return _httpClient.PutAsJsonAsync("company", editedCompanyResponse);
        }

        public Task<HttpResponseMessage> GetGeneralRolesAsync()
        {
            return _httpClient.GetAsync("roles/general-roles");
        }

        public Task<HttpResponseMessage> GetEmployeesByCompanyIdAndPermissionAsync(GetEmployeesByCompanyIdAndPermissionRequest getEmployeesByCompanyIdAndPermissionRequest)
        {
            return _httpClient.GetAsync($"emplyoees/company-and-permission?companyId={getEmployeesByCompanyIdAndPermissionRequest.CompanyId}&permission={getEmployeesByCompanyIdAndPermissionRequest.Permission}");
        }

        public Task<HttpResponseMessage> GetWorkTimeRoleAsync(GetWorkTimeRoleRequest getWorkTimeRoleRequest)
        {
            return _httpClient.GetAsync($"roles/employees/{getWorkTimeRoleRequest.UserId}/worktime");
        }

        public Task<HttpResponseMessage> SetWorkTimeRoleAsync(SetWorkTimeRoleRequest request)
        {
            return _httpClient.PostAsJsonAsync("roles/employee/worktime", request);
        }

        public Task<HttpResponseMessage> LoadCoworkersAsync(LoadCoworkersRequest loadEmployeesRequest)
        {
            return _httpClient.GetAsync($"employees/coworkers?employeeId={loadEmployeesRequest.UserId}");
        }

        public Task<HttpResponseMessage> ChangeEmployeeRoleAsync(ChangeEmployeeRoleRequest request)
        {
            return _httpClient.PostAsJsonAsync($"roles/employee/update", request);
        }

        public Task<HttpResponseMessage> ShareCompanyAsync(ShareCompanyRequest addRoleToEmployee)
        {
            return _httpClient.PostAsJsonAsync("roles/employee/add", addRoleToEmployee);
        }

        public Task<HttpResponseMessage> GetUserEmployeePermissionsAsync(Guid userId, Guid companyId)
        {
            return _httpClient.GetAsync($"user-employee-permissions/{userId}/{companyId}");
        }

        public Task<HttpResponseMessage> GetUserPermissionsAsync(Guid userId, Guid companyId)
        {
            return _httpClient.GetAuthAsync($"user-permissions/{userId}/{companyId}", _httpContextAccessor);
        }

        public Task<HttpResponseMessage> AddNewEmployeePayrollAsync(AddNewEmployeePayrollRequest addNewEmployeePayrollRequest)
        {
            return _httpClient.PostAsJsonAsync($"employee-payroll", addNewEmployeePayrollRequest);
        }

        public Task<HttpResponseMessage> LoadAllUserNotificationsAsync(GetAllUserNotifications getAllUserNotifications)
        {
            return _httpClient.GetAsync($"all-user-notifications?userId={getAllUserNotifications.UserId}");
        }

        public Task<HttpResponseMessage> ReadNotificationAsync(ReadNotificationRequest readNotificationRequest)
        {
            return _httpClient.PostAsJsonAsync("notifications/mark-as-read", readNotificationRequest);
        }

        public Task<HttpResponseMessage> UpdateUserEmailAsync(UpdateUserEmailRequest updateUserEmailRequest)
        {
            return _httpClient.PostAsJsonAsync("users/update-email", updateUserEmailRequest);
        }

        public Task<HttpResponseMessage> ConfirmEmailByCodeAsync(string email, string code)
        {
            return _httpClient.GetAsync($"users/confirm-email-by-worktime-code?email={email}&code={code}");
        }
    }
}
